"""
Definições de tema e estilos para a aplicação PROSPECTO.
"""
import flet as ft

# Esquema de cores principal
# Cores principais
PRIMARY_COLOR = "#1E88E5"  # Azul principal
SECONDARY_COLOR = "#26A69A"  # Verde-azulado secundário
ACCENT_COLOR = "#FF5722"  # Laranja para destaque
BACKGROUND_COLOR = "#F5F7FA"  # Cinza claro para fundo
CARD_COLOR = "#FFFFFF"  # Branco para cards e containers

# Cores de texto
TEXT_PRIMARY = "#212121"  # Quase preto para texto principal
TEXT_SECONDARY = "#757575"  # Cinza para texto secundário
TEXT_HINT = "#9E9E9E"  # Cinza claro para dicas e placeholders

# Cores de estado
SUCCESS_COLOR = "#4CAF50"  # Verde para sucesso
ERROR_COLOR = "#F44336"  # Vermelho para erro
WARNING_COLOR = "#FFC107"  # Amarelo para avisos
INFO_COLOR = "#2196F3"  # Azul para informações

# Cores de botões
BUTTON_PRIMARY = PRIMARY_COLOR
BUTTON_SECONDARY = "#E0E0E0"  # Cinza claro para botões secundários
BUTTON_DISABLED = "#BDBDBD"  # Cinza para botões desabilitados

# Estilos de texto
FONT_FAMILY = "Roboto"
HEADING_SIZE = 24
SUBHEADING_SIZE = 18
BODY_SIZE = 14
CAPTION_SIZE = 12

# Espaçamento
PADDING_SMALL = 8
PADDING_MEDIUM = 16
PADDING_LARGE = 24
PADDING_XLARGE = 32

# Bordas
BORDER_RADIUS_SMALL = 4
BORDER_RADIUS_MEDIUM = 8
BORDER_RADIUS_LARGE = 12

# Sombras
SHADOW_LIGHT = ft.BoxShadow(
    spread_radius=1,
    blur_radius=3,
    color=ft.colors.with_opacity(0.1, "000000"),
    offset=ft.Offset(0, 1)
)

SHADOW_MEDIUM = ft.BoxShadow(
    spread_radius=1,
    blur_radius=5,
    color=ft.colors.with_opacity(0.2, "000000"),
    offset=ft.Offset(0, 2)
)

SHADOW_HEAVY = ft.BoxShadow(
    spread_radius=2,
    blur_radius=10,
    color=ft.colors.with_opacity(0.3, "000000"),
    offset=ft.Offset(0, 4)
)

# Estilos de componentes
def get_page_theme():
    """Retorna o tema da página."""
    return ft.Theme(
        color_scheme=ft.ColorScheme(
            primary=PRIMARY_COLOR,
            secondary=SECONDARY_COLOR,
            background=BACKGROUND_COLOR,
            surface=CARD_COLOR,
            on_primary=ft.colors.WHITE,
            on_secondary=ft.colors.WHITE,
            on_background=TEXT_PRIMARY,
            on_surface=TEXT_PRIMARY,
            error=ERROR_COLOR,
            on_error=ft.colors.WHITE,
        ),
        visual_density=ft.ThemeVisualDensity.COMFORTABLE,
        use_material3=True,
    )

def apply_text_style(text_component, style="body", weight=ft.FontWeight.NORMAL, color=None):
    """
    Aplica estilo de texto a um componente de texto.

    Args:
        text_component (ft.Text): Componente de texto
        style (str): Estilo de texto (heading, subheading, body, caption)
        weight (ft.FontWeight): Peso da fonte
        color (str): Cor do texto
    """
    if style == "heading":
        text_component.size = HEADING_SIZE
        text_component.weight = ft.FontWeight.BOLD if weight == ft.FontWeight.NORMAL else weight
    elif style == "subheading":
        text_component.size = SUBHEADING_SIZE
        text_component.weight = weight
    elif style == "body":
        text_component.size = BODY_SIZE
        text_component.weight = weight
    elif style == "caption":
        text_component.size = CAPTION_SIZE
        text_component.weight = weight

    if color:
        text_component.color = color

    text_component.font_family = FONT_FAMILY
    return text_component

def create_card(content, width=None, height=None, padding=PADDING_MEDIUM, margin=None, elevation=1):
    """
    Cria um card com sombra e bordas arredondadas.

    Args:
        content: Conteúdo do card
        width (int, optional): Largura do card
        height (int, optional): Altura do card
        padding (int, optional): Padding interno do card
        margin (int, optional): Margem do card
        elevation (int, optional): Elevação da sombra (1-3)
    """
    shadow = SHADOW_LIGHT
    if elevation == 2:
        shadow = SHADOW_MEDIUM
    elif elevation >= 3:
        shadow = SHADOW_HEAVY

    return ft.Container(
        content=content,
        width=width,
        height=height,
        padding=padding,
        margin=margin,
        bgcolor=CARD_COLOR,
        border_radius=BORDER_RADIUS_MEDIUM,
        shadow=[shadow]
    )

def create_button(text, icon=None, on_click=None, style="primary", disabled=False):
    """
    Cria um botão estilizado.

    Args:
        text (str): Texto do botão
        icon (str, optional): Ícone do botão
        on_click (function, optional): Função de callback
        style (str, optional): Estilo do botão (primary, secondary, error, success)
        disabled (bool, optional): Se o botão está desabilitado
    """
    color = BUTTON_PRIMARY
    text_color = ft.colors.WHITE

    if style == "secondary":
        color = SECONDARY_COLOR
    elif style == "error":
        color = ERROR_COLOR
    elif style == "success":
        color = SUCCESS_COLOR
    elif style == "warning":
        color = WARNING_COLOR
    elif style == "info":
        color = INFO_COLOR

    return ft.ElevatedButton(
        text=text,
        icon=icon,
        on_click=on_click,
        disabled=disabled,
        bgcolor=color,
        color=text_color,
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=BORDER_RADIUS_SMALL),
            elevation=5,
            animation_duration=300,
        )
    )

def create_text_field(label, hint_text=None, width=None, multiline=False, read_only=False):
    """
    Cria um campo de texto estilizado.

    Args:
        label (str): Rótulo do campo
        hint_text (str, optional): Texto de dica
        width (int, optional): Largura do campo
        multiline (bool, optional): Se o campo é multilinhas
        read_only (bool, optional): Se o campo é somente leitura
    """
    return ft.TextField(
        label=label,
        hint_text=hint_text,
        width=width,
        multiline=multiline,
        read_only=read_only,
        border_radius=BORDER_RADIUS_SMALL,
        focused_border_color=PRIMARY_COLOR,
        focused_color=PRIMARY_COLOR,
        cursor_color=PRIMARY_COLOR,
        text_size=BODY_SIZE,
        label_style=ft.TextStyle(
            color=TEXT_SECONDARY,
            size=BODY_SIZE,
        ),
        hint_style=ft.TextStyle(
            color=TEXT_HINT,
            size=BODY_SIZE,
        ),
    )
