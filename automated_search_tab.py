"""
Interface da aba de busca automatizada para a versão desktop do PROSPECTO.
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QLineEdit, QProgressBar, QFrame, QScrollArea, QRadioButton,
                            QButtonGroup, QFileDialog, QMessageBox, QSpacerItem, QSizePolicy,
                            QDialog, QListWidget, QListWidgetItem, QCheckBox)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon, QPixmap
import os
import config_manager

from automated_search_desktop import AutomatedSearchThread
from desktop_theme import (
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACKGROUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    style_heading_label, style_subheading_label, style_body_label, style_caption_label,
    create_card_frame, style_primary_button, style_secondary_button, style_accent_button
)

from ai_integration import get_search_suggestions, get_profession_suggestions, get_location_suggestions

class SuggestionDialog(QDialog):
    """Diálogo para exibir sugestões de busca."""
    def __init__(self, suggestions, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Sugestões de Busca")
        self.setMinimumWidth(400)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {CARD_COLOR};
                border-radius: 8px;
            }}
        """)

        layout = QVBoxLayout(self)

        # Título
        title = QLabel("Clique em uma sugestão para usá-la:")
        style_subheading_label(title)
        layout.addWidget(title)

        # Lista de sugestões
        self.suggestion_list = QListWidget()
        self.suggestion_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #EEEEEE;
            }}
            QListWidget::item:selected {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
            QListWidget::item:hover:!selected {{
                background-color: #E3F2FD;
            }}
        """)

        for suggestion in suggestions:
            item = QListWidgetItem(suggestion)
            self.suggestion_list.addItem(item)

        self.suggestion_list.itemDoubleClicked.connect(self.accept)
        layout.addWidget(self.suggestion_list)

        # Botão de fechar
        close_button = QPushButton("Fechar")
        style_secondary_button(close_button)
        close_button.clicked.connect(self.reject)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

    def get_selected_suggestion(self):
        """Retorna a sugestão selecionada."""
        items = self.suggestion_list.selectedItems()
        if items:
            return items[0].text()
        return None

class AutomatedSearchTab(QWidget):
    """Aba de busca automatizada no Google Maps."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.search_queries = []
        self.query_fields = []
        self.search_thread = None

        # Carregar configurações
        self.config = config_manager.load_config()

        self.initUI()

    def initUI(self):
        """Inicializa a interface da aba."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Título da aba
        title_label = QLabel("Busca Automatizada no Google Maps")
        style_heading_label(title_label)
        layout.addWidget(title_label)

        # Descrição
        description = QLabel("Configure até 17 consultas de busca para execução automática em sequência.")
        style_body_label(description)
        layout.addWidget(description)

        # Card para consultas de busca
        queries_card = create_card_frame()
        queries_layout = QVBoxLayout(queries_card)

        # Título do card
        queries_title = QLabel("Consultas de Busca")
        style_subheading_label(queries_title)
        queries_layout.addWidget(queries_title)

        # Área de rolagem para as consultas
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {CARD_COLOR};
                border: none;
            }}
        """)

        # Container para os campos de consulta
        scroll_content = QWidget()
        self.queries_container = QVBoxLayout(scroll_content)
        self.queries_container.setSpacing(10)

        scroll_area.setWidget(scroll_content)
        queries_layout.addWidget(scroll_area)

        # Botão para adicionar nova consulta
        add_query_button = QPushButton("Adicionar Nova Consulta")
        add_query_button.setIcon(QIcon.fromTheme("list-add"))
        style_primary_button(add_query_button)
        add_query_button.clicked.connect(self.add_query_field)

        queries_layout.addWidget(add_query_button)
        layout.addWidget(queries_card)

        # Adicionar campos de consulta iniciais (17 como solicitado)
        for i in range(17):
            self.add_query_field()

        # Card para configurações de salvamento
        config_card = create_card_frame()
        config_layout = QVBoxLayout(config_card)

        # Título do card
        config_title = QLabel("Configurações de Salvamento")
        style_subheading_label(config_title)
        config_layout.addWidget(config_title)

        # Diretório para salvar
        save_dir_layout = QHBoxLayout()
        save_dir_label = QLabel("Diretório para Salvar:")
        style_body_label(save_dir_label)
        self.save_dir = QLineEdit()
        self.save_dir.setReadOnly(True)
        self.save_dir.setStyleSheet(f"""
            QLineEdit {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                padding: 8px;
            }}
        """)

        browse_button = QPushButton("Selecionar")
        browse_button.setIcon(QIcon.fromTheme("folder-open"))
        style_secondary_button(browse_button)
        browse_button.clicked.connect(self.select_save_directory)

        save_dir_layout.addWidget(save_dir_label)
        save_dir_layout.addWidget(self.save_dir)
        save_dir_layout.addWidget(browse_button)
        config_layout.addLayout(save_dir_layout)

        # Formato do arquivo
        format_layout = QVBoxLayout()
        format_label = QLabel("Formato do arquivo:")
        style_body_label(format_label)
        format_layout.addWidget(format_label)

        radio_layout = QHBoxLayout()
        self.excel_radio = QRadioButton("Excel")
        self.csv_radio = QRadioButton("CSV")

        # Definir o formato padrão com base nas configurações
        if self.config.get("default_export_format", "excel") == "excel":
            self.excel_radio.setChecked(True)
        else:
            self.csv_radio.setChecked(True)

        radio_layout.addWidget(self.excel_radio)
        radio_layout.addWidget(self.csv_radio)
        radio_layout.addStretch()

        format_layout.addLayout(radio_layout)
        config_layout.addLayout(format_layout)

        # Opção de modo headless
        headless_layout = QHBoxLayout()
        headless_label = QLabel("Modo de Execução:")
        style_body_label(headless_label)
        self.headless_checkbox = QCheckBox("Executar em segundo plano (headless)")
        self.headless_checkbox.setChecked(self.config.get("headless_mode", True))
        self.headless_checkbox.setToolTip("Quando ativado, o navegador funciona em segundo plano sem exibir janela")

        headless_layout.addWidget(headless_label)
        headless_layout.addWidget(self.headless_checkbox)
        headless_layout.addStretch()

        config_layout.addLayout(headless_layout)

        layout.addWidget(config_card)

        # Card para controles de execução
        controls_card = create_card_frame()
        controls_layout = QVBoxLayout(controls_card)

        # Título do card
        controls_title = QLabel("Controles de Execução")
        style_subheading_label(controls_title)
        controls_layout.addWidget(controls_title)

        # Botões de controle
        buttons_layout = QHBoxLayout()

        self.start_button = QPushButton("Iniciar Buscas Automatizadas")
        self.start_button.setIcon(QIcon.fromTheme("media-playback-start"))
        style_primary_button(self.start_button)
        self.start_button.clicked.connect(self.start_automated_search)

        self.pause_button = QPushButton("Pausar")
        self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
        style_secondary_button(self.pause_button)
        self.pause_button.clicked.connect(self.pause_automated_search)
        self.pause_button.setEnabled(False)

        self.stop_button = QPushButton("Parar")
        self.stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: 36px;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
            QPushButton:pressed {{
                background-color: #C62828;
            }}
            QPushButton:disabled {{
                background-color: #BDBDBD;
                color: #9E9E9E;
            }}
        """)
        self.stop_button.clicked.connect(self.stop_automated_search)
        self.stop_button.setEnabled(False)

        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.pause_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addStretch()

        controls_layout.addLayout(buttons_layout)

        # Barra de progresso
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                background-color: #E0E0E0;
                text-align: center;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_COLOR};
            }}
        """)
        controls_layout.addWidget(self.progress_bar)

        # Status
        self.status_label = QLabel("Pronto para iniciar buscas automatizadas")
        style_body_label(self.status_label)
        controls_layout.addWidget(self.status_label)

        layout.addWidget(controls_card)

    def add_query_field(self, query=""):
        """Adiciona um campo de consulta de busca."""
        # Container para o campo e botões
        container = QFrame()
        container.setStyleSheet(f"""
            QFrame {{
                background-color: #F5F5F5;
                border-radius: 4px;
                padding: 5px;
            }}
        """)

        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(5, 5, 5, 5)

        # Campo de texto
        query_field = QLineEdit(query)
        query_field.setPlaceholderText(f"Busca {len(self.query_fields) + 1}: Ex.: 'DENTISTA + RJ'")
        query_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                padding: 8px;
            }}
            QLineEdit:focus {{
                border: 2px solid {PRIMARY_COLOR};
            }}
        """)

        # Botão de sugestões
        suggestion_button = QPushButton()
        suggestion_button.setIcon(QIcon.fromTheme("help-hint"))
        suggestion_button.setToolTip("Obter sugestões com GPT-4 Mini")
        suggestion_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                min-width: 30px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: #E64A19;
            }}
        """)
        suggestion_button.clicked.connect(lambda: self.get_suggestions(query_field))

        # Botão de remoção
        remove_button = QPushButton()
        remove_button.setIcon(QIcon.fromTheme("edit-delete"))
        remove_button.setToolTip("Remover")
        remove_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                min-width: 30px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
        """)
        remove_button.clicked.connect(lambda: self.remove_query_field(container))

        container_layout.addWidget(query_field)
        container_layout.addWidget(suggestion_button)
        container_layout.addWidget(remove_button)

        self.queries_container.addWidget(container)
        self.query_fields.append(query_field)

    def remove_query_field(self, container):
        """Remove um campo de consulta de busca."""
        # Encontrar o índice do campo
        index = self.queries_container.indexOf(container)
        if index >= 0:
            # Remover o widget do layout
            widget = self.queries_container.itemAt(index).widget()
            if widget:
                # Encontrar o QLineEdit dentro do container
                for child in widget.children():
                    if isinstance(child, QLineEdit):
                        self.query_fields.remove(child)
                        break

                # Remover o widget
                widget.setParent(None)
                widget.deleteLater()

                # Atualizar os placeholders
                for i, field in enumerate(self.query_fields):
                    field.setPlaceholderText(f"Busca {i + 1}: Ex.: 'DENTISTA + RJ'")

    def get_suggestions(self, query_field):
        """Obtém sugestões de busca usando GPT-4 Mini."""
        try:
            if not query_field.text():
                # Se o campo estiver vazio, obter sugestões de profissões e localizações
                professions = get_profession_suggestions(5)
                locations = get_location_suggestions(5)
                # Combinar profissões e localizações
                suggestions = [f"{prof} + {loc}" for prof, loc in zip(professions[:5], locations[:5])]
            else:
                # Se o campo tiver texto, obter sugestões baseadas no texto
                suggestions = get_search_suggestions(query_field.text(), 5)

            # Exibir diálogo de sugestões
            dialog = SuggestionDialog(suggestions, self)
            if dialog.exec():
                selected = dialog.get_selected_suggestion()
                if selected:
                    query_field.setText(selected)

        except Exception as e:
            QMessageBox.warning(self, "Erro", f"Erro ao obter sugestões: {str(e)}")

    def select_save_directory(self):
        """Seleciona o diretório para salvar os resultados."""
        # Usar o diretório padrão das configurações como ponto de partida
        default_dir = self.config.get("default_save_dir", "")

        directory = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório",
            default_dir
        )

        if directory:
            self.save_dir.setText(directory)

    def start_automated_search(self):
        """Inicia a busca automatizada."""
        # Validar entradas
        if not self.save_dir.text():
            QMessageBox.warning(self, "Erro", "Por favor, selecione um diretório para salvar os resultados.")
            return

        # Obter consultas de busca
        queries = [field.text() for field in self.query_fields if field.text()]
        if not queries:
            QMessageBox.warning(self, "Erro", "Por favor, adicione pelo menos uma consulta de busca.")
            return

        # Atualizar UI
        self.progress_bar.setValue(0)
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)

        # Obter formato do arquivo e modo de execução
        file_format = "excel" if self.excel_radio.isChecked() else "csv"
        headless_mode = self.headless_checkbox.isChecked()

        # Criar e iniciar thread de busca
        self.search_thread = AutomatedSearchThread(
            search_queries=queries,
            save_dir=self.save_dir.text(),
            file_format=file_format,
            headless_mode=headless_mode
        )

        # Conectar sinais
        self.search_thread.progress_updated.connect(self.progress_bar.setValue)
        self.search_thread.status_updated.connect(self.status_label.setText)
        self.search_thread.finished_signal.connect(self.search_finished)
        self.search_thread.continue_query.connect(self.show_continue_dialog)
        self.search_thread.query_completed.connect(self.query_completed)

        # Iniciar thread
        self.search_thread.start()
        self.status_label.setText("Iniciando buscas automatizadas...")

    def pause_automated_search(self):
        """Pausa ou retoma a busca automatizada."""
        if not self.search_thread:
            return

        if self.search_thread.paused:
            # Retomar
            self.search_thread.resume()
            self.pause_button.setText("Pausar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.status_label.setText("Buscas automatizadas retomadas")
        else:
            # Pausar
            self.search_thread.pause()
            self.pause_button.setText("Continuar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-start"))
            self.status_label.setText("Buscas automatizadas pausadas")

    def stop_automated_search(self):
        """Para a busca automatizada."""
        if not self.search_thread:
            return

        reply = QMessageBox.question(
            self,
            "Confirmar",
            "Tem certeza que deseja interromper as buscas automatizadas?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.search_thread.stop()
            self.search_thread = None

            # Atualizar UI
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.pause_button.setText("Pausar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.status_label.setText("Buscas automatizadas interrompidas")

    def search_finished(self, result):
        """Callback chamado quando a busca automatizada é finalizada."""
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

        if "ERRO" not in result:
            QMessageBox.information(self, "Sucesso", result)
        else:
            QMessageBox.warning(self, "Erro", result)

    def query_completed(self, index):
        """Callback chamado quando uma consulta é concluída."""
        # Destacar visualmente a consulta concluída
        if index < len(self.query_fields):
            container = self.query_fields[index].parent()
            if container:
                container.setStyleSheet(f"""
                    QFrame {{
                        background-color: #E8F5E9;
                        border-radius: 4px;
                        padding: 5px;
                        border: 1px solid {SUCCESS_COLOR};
                    }}
                """)

    def show_continue_dialog(self, message, require_response):
        """Exibe um diálogo perguntando se deseja continuar a busca."""
        if require_response:
            reply = QMessageBox.question(
                self,
                "Continuar Busca",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.search_thread.continue_response = "S"
            else:
                self.search_thread.continue_response = "N"
