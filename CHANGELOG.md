# Changelog

## Version 1.1.0 - Google Maps Integration

### Added
- New Google Maps scraper functionality integrated into the main application
- Tabbed interface with separate tabs for WhatsApp and Google Maps features
- Business data extraction from Google Maps including:
  - Business name
  - Address
  - Phone number
  - Website
  - Rating and reviews count
  - Category
  - Operating hours
  - Geographic coordinates
  - Plus code
  - Place ID
- Option to save extracted data in Excel or CSV format
- Ability to load extracted business data directly into the WhatsApp messaging system
- Automatic phone number formatting for compatibility with WhatsApp
- Interactive map navigation to find more businesses beyond initial search results

### New Classes
- `Business` - Data class for storing business information
- `BusinessList` - Container class for managing collections of businesses
- `GoogleMapsScraperThread` - Background thread for Google Maps scraping operations

### New Methods
- `move_map()` - Utility function to navigate Google Maps in different directions
- `select_save_directory()` - Dialog for selecting where to save extracted data
- `start_gmaps_search()` - Initiates the Google Maps scraping process
- `gmaps_search_finished()` - Handles completion of the scraping process
- `show_continue_dialog()` - Interactive dialog for continuing map exploration
- `load_specific_file()` - Loads extracted data for WhatsApp messaging
- `distribuir_mensagens()` - Distributes message types among contacts

### Improved
- Enhanced UI with tabbed interface for better organization
- Streamlined workflow between data extraction and messaging
- Progress tracking for Google Maps scraping operations
- Status updates during the scraping process

### Technical
- Added necessary Selenium components for web scraping
- Implemented multi-threaded operation to keep UI responsive
- Added error handling for Google Maps scraping edge cases

## Version 1.0.0 - Initial Release

### Features
- WhatsApp Web integration for automated messaging
- Message template system with variable substitution
- Message type distribution (A, B, C) with configurable percentages
- Excel file import for contact lists
- Batch processing with randomized intervals
- Progress tracking and status updates
- Detailed logging of message sending activities
- Template download functionality
- Configurable messaging settings
