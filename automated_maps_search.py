import threading
import time
import os
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin

# Import existing functionality
from google_maps_scraper import move_map, BusinessList

class AutomatedMapSearch:
    """
    Class to handle automated Google Maps searches.
    """
    def __init__(self, search_queries, save_dir, file_format="excel", callback=None):
        """
        Initialize the automated search.
        
        Args:
            search_queries (list): List of search queries
            save_dir (str): Directory to save results
            file_format (str): Format to save results (excel or csv)
            callback (function): Callback function for status updates
        """
        self.search_queries = search_queries
        self.save_dir = save_dir
        self.file_format = file_format
        self.callback = callback
        self.current_query_index = 0
        self.navegador = None
        self.results = []
        self.running = False
        self.paused = False
        
    def start(self):
        """Start the automated search process."""
        if self.running:
            return
            
        self.running = True
        threading.Thread(target=self._run_searches, daemon=True).start()
        
    def pause(self):
        """Pause the automated search process."""
        self.paused = True
        
    def resume(self):
        """Resume the automated search process."""
        self.paused = False
        
    def stop(self):
        """Stop the automated search process."""
        self.running = False
        if self.navegador:
            self.navegador.quit()
            
    def _run_searches(self):
        """Run the automated searches."""
        try:
            # Configure Chrome
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--enable-unsafe-swiftshader")
            chrome_options.add_argument("--disable-software-rasterizer")
            
            self.navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
            
            if self.callback:
                self.callback("[INFO] Abrindo Google Maps...")
                
            self.navegador.get("https://www.google.com.br/maps")
            time.sleep(3)
            
            # Run each search query
            for i, query in enumerate(self.search_queries):
                if not self.running:
                    break
                    
                self.current_query_index = i
                
                # Wait if paused
                while self.paused and self.running:
                    time.sleep(1)
                    
                if self.callback:
                    self.callback(f"[INFO] Executando busca {i+1}/{len(self.search_queries)}: {query}")
                    
                # Clear previous search
                search_box = self.navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]')
                search_box.clear()
                time.sleep(1)
                
                # Enter new search
                search_box.send_keys(query)
                time.sleep(1)
                self.navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
                time.sleep(10)
                
                # Extract business data
                business_list = self._extract_business_data(query)
                
                # Save results
                if business_list.businesses:
                    self._save_results(business_list, query)
                    
                # Wait between searches
                time.sleep(5)
                
            if self.callback:
                self.callback("[INFO] Todas as buscas foram concluídas.")
                
        except Exception as e:
            if self.callback:
                self.callback(f"[ERRO] {str(e)}")
        finally:
            if self.navegador:
                self.navegador.quit()
            self.running = False
                
    def _extract_business_data(self, query, max_results=50):
        """
        Extract business data from Google Maps.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results to extract
            
        Returns:
            BusinessList: List of businesses
        """
        business_list = BusinessList()
        i = 0
        move_count = 0
        max_moves = 4
        
        while i < max_results and move_count < max_moves and self.running:
            # Wait if paused
            while self.paused and self.running:
                time.sleep(1)
                
            previously_counted = 0
            stuck_count = 0
            
            while self.running:
                list_elem = self.navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                if not list_elem:
                    break
                    
                action = ActionChains(self.navegador)
                try:
                    action.move_to_element(list_elem[-1]).perform()
                except Exception:
                    list_elem = self.navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                    if list_elem:
                        action.move_to_element(list_elem[-1]).perform()
                time.sleep(5)
                
                scroll_origin = ScrollOrigin.from_element(list_elem[-1])
                action.scroll_from_origin(scroll_origin, 0, 1200).perform()
                time.sleep(10)
                action.scroll_from_origin(scroll_origin, 0, 250).perform()
                
                current_count = len(list_elem)
                if current_count == previously_counted:
                    stuck_count += 1
                    if stuck_count >= 3:
                        break
                else:
                    stuck_count = 0
                    previously_counted = current_count
                    
            # Process the elements found
            list_elem = self.navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            for element in list_elem[i:]:
                if i >= max_results or not self.running:
                    break
                    
                try:
                    time.sleep(2)
                    self.navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(2)
                    try:
                        element.click()
                    except Exception as click_err:
                        if self.callback:
                            self.callback(f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}")
                        i += 1
                        continue
                    time.sleep(6)
                    
                    # Extract business data
                    business_data = self._extract_single_business()
                    if business_data:
                        business_list.add_business(**business_data)
                        
                    i += 1
                    if self.callback:
                        self.callback(f"[INFO] Extraído {i}/{max_results} negócios")
                        
                    # Close the business details panel
                    try:
                        close_button = self.navegador.find_element(By.XPATH, '//button[@aria-label="Voltar ao resultado da pesquisa"]')
                        close_button.click()
                        time.sleep(2)
                    except:
                        pass
                        
                except Exception as e:
                    if self.callback:
                        self.callback(f"[ERRO] {str(e)}")
                    i += 1
                    
            # If we haven't reached the desired number of results, move the map
            if i < max_results and self.running:
                directions = ['right', 'down', 'left', 'up']
                move_map(self.navegador, directions[move_count % len(directions)])
                move_count += 1
                time.sleep(5)
                
        return business_list
        
    def _extract_single_business(self):
        """
        Extract data for a single business.
        
        Returns:
            dict: Business data
        """
        try:
            # XPaths for data extraction
            name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
            address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
            website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
            phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'
            
            # Extract data
            name = self._get_element_text(name_xpath)
            address = self._get_element_text(address_xpath)
            website = self._get_element_text(website_xpath)
            phone = self._get_element_text(phone_number_xpath)
            
            # Return business data
            return {
                'name': name,
                'address': address,
                'website': website,
                'phone': phone
            }
            
        except Exception as e:
            if self.callback:
                self.callback(f"[ERRO] Falha ao extrair dados do negócio: {str(e)}")
            return None
            
    def _get_element_text(self, xpath):
        """
        Get text from an element.
        
        Args:
            xpath (str): XPath to the element
            
        Returns:
            str: Element text or empty string if not found
        """
        try:
            element = self.navegador.find_element(By.XPATH, xpath)
            return element.text
        except:
            return ""
            
    def _save_results(self, business_list, query):
        """
        Save results to file.
        
        Args:
            business_list (BusinessList): List of businesses
            query (str): Search query
        """
        try:
            # Create a safe filename
            safe_query = query.replace(" ", "_").replace("/", "_").replace("\\", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automated_search_{safe_query}_{timestamp}"
            
            # Save to file
            result = None
            if self.file_format.lower() == "excel":
                result = business_list.save_to_excel(filename, self.save_dir)
            elif self.file_format.lower() == "csv":
                result = business_list.save_to_csv(filename, self.save_dir)
            else:
                result = "Formato de arquivo inválido."
                
            if self.callback:
                self.callback(result)
                
        except Exception as e:
            if self.callback:
                self.callback(f"[ERRO] Falha ao salvar resultados: {str(e)}")
