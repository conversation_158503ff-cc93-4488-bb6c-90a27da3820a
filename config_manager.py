"""
Gerenciador de configurações para o aplicativo PROSPECTO.
Responsável por carregar, salvar e gerenciar as configurações do aplicativo.
"""
import json
from pathlib import Path

# Caminho para o arquivo de configurações
CONFIG_FILE = Path.home() / "prospecto_config.json"

# Configurações padrão
DEFAULT_CONFIG = {
    "ai_model": "GPT-4 Mini",
    "ai_api_key": "",
    "ai_provider": "OpenAI",
    "headless_mode": True,
    "default_save_dir": str(Path.home() / "Documents"),
    "default_export_format": "excel",
    "app_version": "1.0.0"
}

def load_config():
    """
    Carrega as configurações do arquivo. Se o arquivo não existir,
    cria um novo com as configurações padrão.
    """
    if not CONFIG_FILE.exists():
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG.copy()

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Verificar se todas as chaves padrão existem
        for key, value in DEFAULT_CONFIG.items():
            if key not in config:
                config[key] = value

        return config
    except Exception as e:
        print(f"Erro ao carregar configurações: {e}")
        return DEFAULT_CONFIG.copy()

def save_config(config):
    """
    Salva as configurações no arquivo.
    """
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Erro ao salvar configurações: {e}")
        return False

def reset_config():
    """
    Restaura as configurações para os valores padrão.
    """
    save_config(DEFAULT_CONFIG)
    return DEFAULT_CONFIG.copy()

def get_config_value(key, default=None):
    """
    Obtém um valor específico das configurações.
    """
    config = load_config()
    return config.get(key, default)

def set_config_value(key, value):
    """
    Define um valor específico nas configurações.
    """
    config = load_config()
    config[key] = value
    return save_config(config)
