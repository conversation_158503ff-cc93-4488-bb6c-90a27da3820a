import flet as ft
from web_interface import create_interface
from theme import get_page_theme, BACKGROUND_COLOR

def main(page: ft.Page):
    """
    Main function for the web application.

    Args:
        page (ft.Page): The Flet page object
    """
    # Configure the page
    page.title = "PROSPECTO - Lead Generation Tool"
    page.window_width = 1200
    page.window_height = 900
    page.padding = 0  # Removido padding para layout mais profissional
    page.theme_mode = "light"
    page.window_icon = "assets/img/logo.png"  # Usando o logo correto
    page.bgcolor = BACKGROUND_COLOR
    page.theme = get_page_theme()
    page.fonts = {
        "Roboto": "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap",
    }

    # Habilitar animações e transições suaves
    page.scroll = ft.ScrollMode.AUTO

    # Create the interface
    create_interface(page)

if __name__ == "__main__":
    ft.app(target=main)
