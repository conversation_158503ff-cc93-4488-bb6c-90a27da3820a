"""
Utility to generate a batch of message variations.
"""
import argparse
import json
import csv
from datetime import datetime

from gerar_mensagem import generate_multiple_variations
from utils.db_operations import save_message

def generate_batch(example_message, count, temperature=None, version="v1", output_file=None):
    """
    Generate a batch of message variations.
    
    Args:
        example_message (str): The example message
        count (int): Number of variations to generate
        temperature (float, optional): Temperature parameter for generation
        version (str, optional): Version tag for the generated messages. Defaults to "v1".
        output_file (str, optional): Path to save the generated messages to a CSV file. Defaults to None.
        
    Returns:
        tuple: (list of generated messages, number of saved messages)
    """
    # Generate the variations
    variations = generate_multiple_variations(example_message, count, temperature)
    
    # Save the variations to the database
    saved_count = 0
    for message in variations:
        if save_message(message, version):
            saved_count += 1
    
    # Save to CSV if requested
    if output_file:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['message', 'timestamp', 'version']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for message in variations:
                writer.writerow({
                    'message': message,
                    'timestamp': datetime.now().isoformat(),
                    'version': version
                })
    
    return variations, saved_count

def main():
    """Main function to generate a batch of message variations."""
    parser = argparse.ArgumentParser(description="Generate a batch of message variations")
    parser.add_argument("--example", "-e", required=True, help="Example message to generate variations of")
    parser.add_argument("--count", "-c", type=int, default=10, help="Number of variations to generate")
    parser.add_argument("--temperature", "-t", type=float, default=1.0, help="Temperature parameter for generation")
    parser.add_argument("--version", "-v", default="v1", help="Version tag for the generated messages")
    parser.add_argument("--output", "-o", help="Path to save the generated messages to a CSV file")
    
    args = parser.parse_args()
    
    try:
        print(f"Generating {args.count} message variations...")
        variations, saved_count = generate_batch(
            args.example, args.count, args.temperature, args.version, args.output
        )
        
        print(f"Generated {len(variations)} variations.")
        print(f"Saved {saved_count} unique variations to the database.")
        
        if args.output:
            print(f"Saved all variations to {args.output}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
