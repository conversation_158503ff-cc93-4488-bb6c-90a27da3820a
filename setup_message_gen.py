"""
Initialization script for the message generation and management system.
"""
import os
import sys
from pathlib import Path

def check_python_version():
    """Check if the Python version is compatible."""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required.")
        sys.exit(1)
    print(f"Python version: {sys.version}")

def check_dependencies():
    """Check if the required dependencies are installed."""
    try:
        import openai
        import dotenv
        print("All required dependencies are installed.")
    except ImportError as e:
        print(f"Error: Missing dependency - {e}")
        print("Please install the required dependencies:")
        print("pip install -r requirements_message_gen.txt")
        sys.exit(1)

def check_api_key():
    """Check if the OpenAI API key is set."""
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("Warning: OPENAI_API_KEY environment variable is not set.")
        print("You will need to set it before using the message generation functionality:")
        print("set OPENAI_API_KEY=your_api_key  # Windows")
        print("export OPENAI_API_KEY=your_api_key  # Linux/Mac")
    else:
        print("OpenAI API key is set.")

def create_directories():
    """Create the required directories if they don't exist."""
    dirs = ["mensagens", "utils"]
    for dir_name in dirs:
        os.makedirs(dir_name, exist_ok=True)
    print("Required directories created.")

def initialize_database():
    """Initialize the database."""
    try:
        from db_setup import initialize_database
        initialize_database()
    except Exception as e:
        print(f"Error initializing database: {e}")
        sys.exit(1)

def main():
    """Main function to set up the system."""
    print("Setting up the message generation and management system...")
    
    check_python_version()
    check_dependencies()
    check_api_key()
    create_directories()
    initialize_database()
    
    print("\nSetup completed successfully!")
    print("\nYou can now use the system to generate and manage messages.")
    print("\nExample usage:")
    print("python gerar_mensagem.py --example \"Olá, tudo bem? Me chamo Claudio...\" --count 3")
    print("python enviar_mensagem.py --action get")

if __name__ == "__main__":
    main()
