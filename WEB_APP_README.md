# PROSPECTO Web Application

Uma aplicação web para geração de leads a partir do Google Maps, com funcionalidades de mensagens e busca automatizada.

## Funcionalidades

### 1. Aba de Mensagens
- Interface para comunicação com leads
- Envio de mensagens personalizadas

### 2. Aba do Google Maps
- Busca manual no Google Maps
- Extração de dados de empresas e profissionais
- Salvamento em formato Excel ou CSV

### 3. Aba de Busca Automatizada no Google Maps
- Execução de 17 buscas consecutivas automaticamente
- Personalização de todas as consultas de busca
- Sugestões de busca alimentadas por GPT-4 Mini
- Automação para buscar 57 empresas em 17 consultas diferentes
- Armazenamento e organização dos resultados para geração de leads

## Requisitos

- Python 3.8+
- Dependências listadas em `web_app_requirements.txt`
- Google Chrome instalado
- Conexão com a internet

## Instalação

1. Instale as dependências:
```bash
pip install -r web_app_requirements.txt
```

2. Execute a aplicação:
```bash
python web_app.py
```

## Uso

### Aba de Mensagens
1. Insira o número de telefone do destinatário
2. Digite sua mensagem
3. Clique em "Enviar Mensagem"

### Aba do Google Maps
1. Insira o termo de busca (ex.: "Restaurante")
2. Insira a localização (ex.: "Rio de Janeiro")
3. Defina o número total de resultados desejados
4. Selecione o diretório para salvar os resultados
5. Escolha o formato do arquivo (Excel ou CSV)
6. Clique em "Executar Consulta"

### Aba de Busca Automatizada
1. Configure até 17 consultas de busca (ex.: "DENTISTA + RJ", "ADVOGADO + SP")
   - Use o botão de sugestões para obter ideias de busca do GPT-4 Mini
2. Selecione o diretório para salvar os resultados
3. Escolha o formato do arquivo (Excel ou CSV)
4. Clique em "Iniciar Buscas Automatizadas"
5. Use os botões "Pausar" e "Parar" para controlar o processo

## Exemplos de Consultas de Busca

1. "DENTISTA + RJ" (Dentistas no Rio de Janeiro)
2. "ADVOGADO + SP" (Advogados em São Paulo)
3. "CONTADOR + MG" (Contadores em Minas Gerais)
4. "MÉDICO + BA" (Médicos na Bahia)
5. "ARQUITETO + PR" (Arquitetos no Paraná)
6. "ENGENHEIRO + RS" (Engenheiros no Rio Grande do Sul)
7. "PSICÓLOGO + PE" (Psicólogos em Pernambuco)
8. "FISIOTERAPEUTA + CE" (Fisioterapeutas no Ceará)
9. "NUTRICIONISTA + DF" (Nutricionistas no Distrito Federal)
10. "PERSONAL TRAINER + GO" (Personal Trainers em Goiás)
11. "CONSULTOR FINANCEIRO + SC" (Consultores Financeiros em Santa Catarina)
12. "CORRETOR IMOBILIÁRIO + ES" (Corretores Imobiliários no Espírito Santo)
13. "DESIGNER + AM" (Designers no Amazonas)
14. "FOTÓGRAFO + PA" (Fotógrafos no Pará)
15. "VETERINÁRIO + MT" (Veterinários no Mato Grosso)
16. "PROFESSOR PARTICULAR + MS" (Professores Particulares no Mato Grosso do Sul)
17. "CONSULTOR DE MARKETING + AL" (Consultores de Marketing em Alagoas)

## Notas

- A aplicação utiliza automação do navegador para extrair dados do Google Maps
- Os resultados são salvos em arquivos Excel ou CSV para fácil manipulação
- A funcionalidade de sugestões de busca utiliza a API do OpenAI (GPT-4 Mini)
- A aplicação é projetada para ajudar na coleta de 1000 leads de negócios

## Suporte

Para suporte ou dúvidas, entre em contato com a equipe de desenvolvimento.
