"""
Definições de tema e estilos para a aplicação desktop PROSPECTO.
"""
from PyQt6.QtWidgets import (QWidget, QPushButton, QLineEdit, QLabel, QFrame,
                            QTabWidget, QGroupBox, QTextEdit, QComboBox)
from PyQt6.QtGui import QColor, QFont, QPalette, QIcon, QPixmap
from PyQt6.QtCore import Qt, QSize
import os

# Esquema de cores principal
# Cores principais
PRIMARY_COLOR = "#1E88E5"  # Azul principal
SECONDARY_COLOR = "#26A69A"  # Verde-azulado secundário
ACCENT_COLOR = "#FF5722"  # Laranja para destaque
BACKGROUND_COLOR = "#F5F7FA"  # Cinza claro para fundo
CARD_COLOR = "#FFFFFF"  # Branco para cards e containers

# Cores de texto
TEXT_PRIMARY = "#212121"  # Quase preto para texto principal
TEXT_SECONDARY = "#757575"  # Cinza para texto secundário
TEXT_HINT = "#9E9E9E"  # Cinza claro para dicas e placeholders

# Cores de estado
SUCCESS_COLOR = "#4CAF50"  # Verde para sucesso
ERROR_COLOR = "#F44336"  # Vermelho para erro
WARNING_COLOR = "#FFC107"  # Amarelo para avisos
INFO_COLOR = "#2196F3"  # Azul para informações

# Cores de botões
BUTTON_PRIMARY = PRIMARY_COLOR
BUTTON_SECONDARY = "#E0E0E0"  # Cinza claro para botões secundários
BUTTON_DISABLED = "#BDBDBD"  # Cinza para botões desabilitados

# Estilos de texto
FONT_FAMILY = "Roboto"
HEADING_SIZE = 24
SUBHEADING_SIZE = 18
BODY_SIZE = 14
CAPTION_SIZE = 12

# Espaçamento
PADDING_SMALL = 8
PADDING_MEDIUM = 16
PADDING_LARGE = 24
PADDING_XLARGE = 32

# Bordas
BORDER_RADIUS_SMALL = 4
BORDER_RADIUS_MEDIUM = 8
BORDER_RADIUS_LARGE = 12

def get_application_palette():
    """Retorna a paleta de cores para a aplicação."""
    palette = QPalette()
    
    # Cores de fundo
    palette.setColor(QPalette.ColorRole.Window, QColor(BACKGROUND_COLOR))
    palette.setColor(QPalette.ColorRole.Base, QColor(CARD_COLOR))
    
    # Cores de texto
    palette.setColor(QPalette.ColorRole.WindowText, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.Text, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.PlaceholderText, QColor(TEXT_HINT))
    
    # Cores de botões
    palette.setColor(QPalette.ColorRole.Button, QColor(BUTTON_PRIMARY))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor("white"))
    
    # Cores de destaque
    palette.setColor(QPalette.ColorRole.Highlight, QColor(PRIMARY_COLOR))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor("white"))
    
    return palette

def get_application_stylesheet():
    """Retorna o stylesheet para a aplicação."""
    return f"""
        /* Estilo geral */
        QWidget {{
            font-family: {FONT_FAMILY}, Arial, sans-serif;
            font-size: {BODY_SIZE}px;
            color: {TEXT_PRIMARY};
        }}
        
        /* Botões */
        QPushButton {{
            background-color: {BUTTON_PRIMARY};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            font-weight: bold;
            min-height: 36px;
        }}
        
        QPushButton:hover {{
            background-color: #1976D2;  /* Versão mais escura do PRIMARY_COLOR */
        }}
        
        QPushButton:pressed {{
            background-color: #1565C0;  /* Versão ainda mais escura */
        }}
        
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: #9E9E9E;
        }}
        
        QPushButton.secondary {{
            background-color: {SECONDARY_COLOR};
        }}
        
        QPushButton.secondary:hover {{
            background-color: #00897B;  /* Versão mais escura do SECONDARY_COLOR */
        }}
        
        QPushButton.accent {{
            background-color: {ACCENT_COLOR};
        }}
        
        QPushButton.accent:hover {{
            background-color: #E64A19;  /* Versão mais escura do ACCENT_COLOR */
        }}
        
        QPushButton.success {{
            background-color: {SUCCESS_COLOR};
        }}
        
        QPushButton.error {{
            background-color: {ERROR_COLOR};
        }}
        
        QPushButton.warning {{
            background-color: {WARNING_COLOR};
            color: {TEXT_PRIMARY};
        }}
        
        QPushButton.info {{
            background-color: {INFO_COLOR};
        }}
        
        /* Campos de texto */
        QLineEdit, QTextEdit, QComboBox {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px;
            background-color: {CARD_COLOR};
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border: 2px solid {PRIMARY_COLOR};
        }}
        
        /* Abas */
        QTabWidget::pane {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_SMALL}px;
            background-color: {CARD_COLOR};
        }}
        
        QTabBar::tab {{
            background-color: #E0E0E0;
            color: {TEXT_SECONDARY};
            border-top-left-radius: {BORDER_RADIUS_SMALL}px;
            border-top-right-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {PRIMARY_COLOR};
            color: white;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: #BDBDBD;
        }}
        
        /* Grupos */
        QGroupBox {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_MEDIUM}px;
            margin-top: 1.5ex;
            background-color: {CARD_COLOR};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 0 {PADDING_SMALL}px;
            color: {TEXT_SECONDARY};
            font-weight: bold;
        }}
        
        /* Frames */
        QFrame.card {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_MEDIUM}px;
            background-color: {CARD_COLOR};
        }}
        
        /* Barras de progresso */
        QProgressBar {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_SMALL}px;
            background-color: #E0E0E0;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {PRIMARY_COLOR};
            width: 10px;
        }}
        
        /* Cabeçalhos de tabelas */
        QHeaderView::section {{
            background-color: {PRIMARY_COLOR};
            color: white;
            padding: {PADDING_SMALL}px;
            border: none;
        }}
        
        /* Tabelas */
        QTableWidget {{
            gridline-color: #BDBDBD;
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            border: none;
            background: #F5F5F5;
            width: 10px;
            margin: 0px;
        }}
        
        QScrollBar::handle:vertical {{
            background: #BDBDBD;
            min-height: 20px;
            border-radius: 5px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: #9E9E9E;
        }}
        
        QScrollBar:horizontal {{
            border: none;
            background: #F5F5F5;
            height: 10px;
            margin: 0px;
        }}
        
        QScrollBar::handle:horizontal {{
            background: #BDBDBD;
            min-width: 20px;
            border-radius: 5px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background: #9E9E9E;
        }}
    """

def style_heading_label(label, size=HEADING_SIZE):
    """Estiliza um QLabel como título."""
    font = QFont(FONT_FAMILY, size)
    font.setBold(True)
    label.setFont(font)
    label.setStyleSheet(f"color: {PRIMARY_COLOR};")
    return label

def style_subheading_label(label, size=SUBHEADING_SIZE):
    """Estiliza um QLabel como subtítulo."""
    font = QFont(FONT_FAMILY, size)
    font.setBold(True)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_PRIMARY};")
    return label

def style_body_label(label, size=BODY_SIZE):
    """Estiliza um QLabel como texto normal."""
    font = QFont(FONT_FAMILY, size)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_PRIMARY};")
    return label

def style_caption_label(label, size=CAPTION_SIZE):
    """Estiliza um QLabel como legenda."""
    font = QFont(FONT_FAMILY, size)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_SECONDARY};")
    return label

def create_card_frame(parent=None):
    """Cria um QFrame estilizado como card."""
    frame = QFrame(parent)
    frame.setFrameShape(QFrame.Shape.StyledPanel)
    frame.setFrameShadow(QFrame.Shadow.Raised)
    frame.setStyleSheet(f"""
        QFrame {{
            background-color: {CARD_COLOR};
            border-radius: {BORDER_RADIUS_MEDIUM}px;
            border: 1px solid #BDBDBD;
        }}
    """)
    return frame

def style_primary_button(button):
    """Estiliza um QPushButton como botão primário."""
    button.setStyleSheet(f"""
        QPushButton {{
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            font-weight: bold;
            min-height: 36px;
        }}
        QPushButton:hover {{
            background-color: #1976D2;
        }}
        QPushButton:pressed {{
            background-color: #1565C0;
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: #9E9E9E;
        }}
    """)
    return button

def style_secondary_button(button):
    """Estiliza um QPushButton como botão secundário."""
    button.setStyleSheet(f"""
        QPushButton {{
            background-color: {SECONDARY_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            font-weight: bold;
            min-height: 36px;
        }}
        QPushButton:hover {{
            background-color: #00897B;
        }}
        QPushButton:pressed {{
            background-color: #00796B;
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: #9E9E9E;
        }}
    """)
    return button

def style_accent_button(button):
    """Estiliza um QPushButton como botão de destaque."""
    button.setStyleSheet(f"""
        QPushButton {{
            background-color: {ACCENT_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            font-weight: bold;
            min-height: 36px;
        }}
        QPushButton:hover {{
            background-color: #E64A19;
        }}
        QPushButton:pressed {{
            background-color: #D84315;
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: #9E9E9E;
        }}
    """)
    return button

def get_logo_pixmap(size=QSize(80, 80)):
    """Retorna um QPixmap com o logo do PROSPECTO."""
    logo_path = os.path.join("assets", "img", "logo.png")
    if os.path.exists(logo_path):
        return QPixmap(logo_path).scaled(size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
    return None
