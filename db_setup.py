"""
Database initialization and schema setup for the message management system.
"""
import sqlite3
import os
from datetime import datetime
import config

def create_tables():
    """Create the necessary tables if they don't exist."""
    conn = sqlite3.connect(config.DB_PATH)
    cursor = conn.cursor()
    
    # Create messages table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message TEXT NOT NULL,
        message_hash TEXT NOT NULL UNIQUE,
        created_at TEXT NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        last_sent TEXT,
        version TEXT DEFAULT 'v1',
        conversion BOOLEAN DEFAULT FALSE,
        uuid TEXT UNIQUE
    )
    ''')
    
    # Create index on hash for faster lookups
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_message_hash ON messages(message_hash)')
    
    # Create index on used status for faster filtering
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_used ON messages(used)')
    
    conn.commit()
    conn.close()
    
    print("Database tables created successfully.")

def initialize_database():
    """Initialize the database if it doesn't exist."""
    if not os.path.exists(config.DB_PATH):
        create_tables()
        print(f"Database initialized at {config.DB_PATH}")
    else:
        print(f"Database already exists at {config.DB_PATH}")

if __name__ == "__main__":
    initialize_database()
