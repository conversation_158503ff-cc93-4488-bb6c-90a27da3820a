"""
Utility to check the similarity between messages.
"""
import argparse
from utils.filtro_similaridade import similaridade

def check_similarity(message1, message2):
    """
    Check the similarity between two messages.
    
    Args:
        message1 (str): First message
        message2 (str): Second message
        
    Returns:
        float: Similarity ratio between 0.0 and 1.0
    """
    return similaridade(message1, message2)

def main():
    """Main function to check similarity between messages."""
    parser = argparse.ArgumentParser(description="Check similarity between messages")
    parser.add_argument("--message1", "-m1", required=True, help="First message")
    parser.add_argument("--message2", "-m2", required=True, help="Second message")
    
    args = parser.parse_args()
    
    try:
        similarity = check_similarity(args.message1, args.message2)
        
        print(f"Similarity ratio: {similarity:.4f}")
        
        if similarity >= 0.95:
            print("The messages are very similar (>= 0.95) and would be considered duplicates.")
        elif similarity >= 0.8:
            print("The messages are quite similar (>= 0.8) but would not be considered duplicates.")
        elif similarity >= 0.6:
            print("The messages have moderate similarity (>= 0.6).")
        else:
            print("The messages are not very similar (< 0.6).")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
