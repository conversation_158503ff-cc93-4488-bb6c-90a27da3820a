"""
Test script for OpenAI API integration using the exact example provided.
"""
from openai import OpenAI

# Create client with the provided API key
client = OpenAI(
  api_key="********************************************************************************************************************************************************************"
)

# Generate a haiku about AI as in the example
completion = client.chat.completions.create(
  model="gpt-4o-mini",
  store=True,
  messages=[
    {"role": "user", "content": "write a haiku about ai"}
  ]
)

# Print the response
print("Response:")
print(completion.choices[0].message)
