# Sistema de Geração e Gerenciamento de Mensagens para PROSPECTO

Este módulo complementa o PROSPECTO com funcionalidades para gerar variações de mensagens usando a API do GPT-4o-mini, armazenar essas mensagens em um banco de dados, e gerenciar seu uso para evitar repetições e detecção como spam.

## Características

- Geração de variações de mensagens usando a API do OpenAI (GPT-4o-mini)
- Detecção de similaridade para evitar mensagens duplicadas
- Armazenamento em banco de dados SQLite
- Rastreamento de uso de mensagens
- Exportação de mensagens para CSV

## Requisitos

- Python 3.8+
- Dependências listadas em `requirements_message_gen.txt`
- Chave de API do OpenAI

## Instalação

1. Instale as dependências adicionais:

```bash
pip install -r requirements_message_gen.txt
```

2. A chave de API do OpenAI já está configurada no arquivo `config.py`.

3. Inicialize o banco de dados:

```bash
python db_setup.py
```

## Uso

### Gerar Mensagens

Para gerar variações de uma mensagem de exemplo:

```bash
python gerar_mensagem.py --example "Olá, tudo bem? Me chamo Claudio, sou contador e especializado em clínicas odontológicas em Madureira. Tenho ajudado consultórios a economizar impostos e manter a regularidade fiscal. Podemos conversar rapidamente por aqui?" --count 3
```

Opções:
- `--example`, `-e`: Mensagem de exemplo (obrigatório)
- `--count`, `-c`: Número de variações a gerar (padrão: 3)
- `--temperature`, `-t`: Parâmetro de temperatura para geração (padrão: 1.0)
- `--version`, `-v`: Tag de versão para as mensagens geradas (padrão: "v1")

### Obter e Marcar Mensagens

Para obter a próxima mensagem não utilizada:

```bash
python enviar_mensagem.py --action get
```

Para marcar uma mensagem como enviada:

```bash
python enviar_mensagem.py --action mark --id 1
```

Para marcar uma mensagem como enviada e que resultou em conversão:

```bash
python enviar_mensagem.py --action mark --id 1 --conversion
```

Para exportar mensagens enviadas para CSV:

```bash
python enviar_mensagem.py --action export
```

## Integração com PROSPECTO

Este módulo pode ser integrado ao PROSPECTO para:

1. Gerar variações de mensagens antes do envio
2. Evitar repetição de mensagens para o mesmo contato
3. Rastrear quais mensagens resultam em mais conversões

### Exemplo de Integração

```python
# No código do PROSPECTO, antes de enviar mensagens
import json
import subprocess

# Obter uma mensagem não utilizada
result = subprocess.run(["python", "enviar_mensagem.py", "--action", "get"], capture_output=True, text=True)
message_data = json.loads(result.stdout)

# Usar a mensagem
message_id = message_data["id"]
message_text = message_data["message"]

# Após enviar, marcar como utilizada
subprocess.run(["python", "enviar_mensagem.py", "--action", "mark", "--id", str(message_id)])

# Se houve conversão, marcar
subprocess.run(["python", "enviar_mensagem.py", "--action", "mark", "--id", str(message_id), "--conversion"])
```

## Custo Estimado

Com o modelo GPT-4o-mini:
- Input: $0.00015 / 1.000 tokens
- Output: $0.00060 / 1.000 tokens

Custo médio por mensagem gerada: ~$0.00015

Com $1, você pode gerar aproximadamente 6.600 mensagens únicas.

## Melhores Práticas

1. Use temperatura alta (1.0-1.3) para maior variação
2. Evite palavras repetidas em sequência de mensagens
3. Intercale com perguntas diferentes ou CTAs alternados
4. Evite enviar em massa para usuários com padrões de comportamento semelhantes
