"""
Test script for message generation.
"""
from gerar_mensagem import generate_message_variation

def main():
    """Test message generation."""
    example_message = "<PERSON><PERSON><PERSON>, tudo bem? Me chamo Claudio, sou contador e especializado em clínicas odontológicas em Madureira. Tenho ajudado consultórios a economizar impostos e manter a regularidade fiscal. Podemos conversar rapidamente por aqui?"
    
    print("Generating message variation...")
    variation = generate_message_variation(example_message)
    
    print("\nOriginal message:")
    print(example_message)
    
    print("\nGenerated variation:")
    print(variation)

if __name__ == "__main__":
    main()
