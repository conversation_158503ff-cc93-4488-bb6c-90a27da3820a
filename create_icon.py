from PIL import Image, ImageDraw, ImageFont
import os

# Criar uma nova imagem com fundo branco (tamanho menor por ser um ícone)
width = 256
height = 256
background_color = (255, 255, 255)  # Branco
image = Image.new('RGB', (width, height), background_color)

# Preparar para desenhar na imagem
draw = ImageDraw.Draw(image)

# Definir a fonte e tamanho
font_size = 32  # Tamanho menor para caber melhor no ícone
try:
    font = ImageFont.truetype("arial.ttf", font_size)
except:
    font = ImageFont.load_default()

# Texto a ser escrito
text = "WIM"  # Abreviação de "PROSPECTO" para melhor legibilidade no ícone

# Calcular a posição do texto para centralizá-lo
bbox = draw.textbbox((0, 0), text, font=font)
text_width = bbox[2] - bbox[0]
text_height = bbox[3] - bbox[1]
x = (width - text_width) / 2
y = (height - text_height) / 2

# Desenhar o texto em preto
text_color = (0, 0, 0)  # Preto
draw.text((x, y), text, fill=text_color, font=font)

# Salvar a imagem como ICO
output_path = "scrap_google_maps/logo.ico"
# Convertendo para modo RGBA antes de salvar como ICO
image_rgba = image.convert('RGBA')
image_rgba.save(output_path, format='ICO', sizes=[(256, 256)])
print(f"Ícone criado com sucesso em: {output_path}")