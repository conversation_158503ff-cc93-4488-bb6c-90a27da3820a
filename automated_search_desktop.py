"""
Implementação da funcionalidade de busca automatizada para a versão desktop do PROSPECTO.
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QLineEdit, QProgressBar, QFrame, QScrollArea, QRadioButton,
                            QButtonGroup, QFileDialog, QMessageBox, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QPixmap
import pandas as pd
import time
import os
import threading
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin

# Importar funcionalidades existentes
# Definir classes Business e BusinessList aqui para evitar importação circular
from dataclasses import dataclass, asdict, field
import pandas as pd

@dataclass
class Business:
    """Representa um negócio extraído do Google Maps."""
    name: str = ""
    address: str = ""
    phone_number: str = ""
    website: str = ""
    rating: str = ""
    reviews_count: str = ""
    category: str = ""
    timetable: str = ""
    latitude: str = ""
    longitude: str = ""
    plus_code: str = ""
    place_id: str = ""

@dataclass
class BusinessList:
    """Armazena uma lista de objetos Business e permite salvar os resultados em Excel ou CSV."""
    business_list: list[Business] = field(default_factory=list)

    def dataframe(self):
        """Transforma a lista de negócios em um DataFrame do pandas."""
        return pd.json_normalize((asdict(business) for business in self.business_list), sep="_")

    def save_to_excel(self, filename, save_dir):
        """Salva o DataFrame em um arquivo Excel."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.xlsx'
            self.dataframe().to_excel(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

    def save_to_csv(self, filename, save_dir):
        """Salva o DataFrame em um arquivo CSV."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.csv'
            self.dataframe().to_csv(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

# Função para mover o mapa
def move_map(navegador, direction='right'):
    """
    Move o mapa na direção especificada.
    direction: 'right', 'left', 'up', 'down'
    """
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
    import time

    # Primeiro clica no mapa para garantir que está focado
    map_element = navegador.find_element(By.CLASS_NAME, 'widget-scene')
    action = ActionChains(navegador)
    action.move_to_element(map_element).click().perform()
    time.sleep(1)

    # Define quantas vezes pressionar a tecla de seta
    presses = 10

    # Mapeia a direção para a tecla correspondente
    key_map = {
        'right': Keys.ARROW_RIGHT,
        'left': Keys.ARROW_LEFT,
        'up': Keys.ARROW_UP,
        'down': Keys.ARROW_DOWN
    }

    # Pressiona a tecla várias vezes para mover o mapa
    key = key_map.get(direction, Keys.ARROW_RIGHT)
    for _ in range(presses):
        action.send_keys(key).perform()
        time.sleep(0.1)

    # Aguarda um pouco para o mapa carregar
    time.sleep(3)

# Importar tema e estilos
from desktop_theme import (
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACKGROUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    style_heading_label, style_subheading_label, style_body_label, style_caption_label,
    create_card_frame, style_primary_button, style_secondary_button, style_accent_button
)

# Importar funcionalidade de sugestões GPT
try:
    from gpt_autocomplete import get_search_suggestions, get_profession_suggestions, get_location_suggestions
except ImportError:
    # Funções de fallback caso o módulo não esteja disponível
    def get_search_suggestions(partial_query, num_suggestions=5):
        return [
            "DENTISTA + RJ",
            "ADVOGADO + SP",
            "CONTADOR + MG",
            "MÉDICO + BA",
            "ARQUITETO + PR"
        ][:num_suggestions]

    def get_profession_suggestions(num_suggestions=10):
        return [
            "DENTISTA",
            "ADVOGADO",
            "CONTADOR",
            "MÉDICO",
            "ARQUITETO",
            "ENGENHEIRO",
            "PSICÓLOGO",
            "FISIOTERAPEUTA",
            "NUTRICIONISTA",
            "PERSONAL TRAINER"
        ][:num_suggestions]

    def get_location_suggestions(num_suggestions=10):
        return [
            "RJ",
            "SP",
            "MG",
            "BA",
            "PR",
            "RS",
            "PE",
            "CE",
            "DF",
            "GO"
        ][:num_suggestions]

class AutomatedSearchThread(QThread):
    """Thread para executar buscas automatizadas no Google Maps."""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    continue_query = pyqtSignal(str, bool)
    query_completed = pyqtSignal(int)  # Sinal para indicar que uma consulta foi concluída

    def __init__(self, search_queries, save_dir, file_format, headless_mode=True):
        super().__init__()
        self.search_queries = search_queries
        self.save_dir = save_dir
        self.file_format = file_format
        self.headless_mode = headless_mode
        self.current_query_index = 0
        self.running = False
        self.paused = False
        self.continue_response = None

    def run(self):
        self.running = True
        try:
            # Configura o Chrome com as opções adequadas
            chrome_options = Options()

            # Configurações para modo headless (se ativado)
            if self.headless_mode:
                self.status_updated.emit("[INFO] Iniciando Chrome em modo headless (segundo plano)...")
                chrome_options.add_argument("--headless=new")  # Novo modo headless do Chrome
                chrome_options.add_argument("--window-size=1920,1080")  # Tamanho da janela virtual
            else:
                self.status_updated.emit("[INFO] Iniciando Chrome em modo visível...")
                chrome_options.add_argument("--start-maximized")  # Maximizar janela

            # Configurações para evitar problemas com GPU e melhorar estabilidade
            chrome_options.add_argument("--disable-gpu")  # Desativa aceleração por hardware
            chrome_options.add_argument("--disable-dev-shm-usage")  # Evita problemas com memória compartilhada
            chrome_options.add_argument("--no-sandbox")  # Desativa o sandbox para evitar problemas
            chrome_options.add_argument("--disable-extensions")  # Desativa extensões
            chrome_options.add_argument("--disable-software-rasterizer")  # Evita problemas com renderização
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")  # Desativa o compositor de exibição
            chrome_options.add_argument("--disable-features=UseOzonePlatform")  # Desativa a plataforma Ozone
            chrome_options.add_argument("--disable-features=TensorFlowLite")  # Desativa TensorFlow Lite
            chrome_options.add_argument("--disable-features=TensorFlowLiteDelegate")  # Desativa delegados do TensorFlow Lite

            # Configurações adicionais para melhorar desempenho em modo headless
            chrome_options.add_argument("--disable-notifications")  # Desativa notificações
            chrome_options.add_argument("--disable-infobars")  # Desativa barras de informação
            chrome_options.add_argument("--mute-audio")  # Desativa áudio
            chrome_options.add_argument("--disable-popup-blocking")  # Permite popups (necessário para alguns sites)
            chrome_options.add_argument("--log-level=3")  # Minimiza logs

            # Configurar user-agent para evitar detecção de headless
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

            self.status_updated.emit("[INFO] Abrindo Google Maps...")
            navegador.get("https://www.google.com.br/maps")
            time.sleep(3)

            # Executar cada consulta de busca
            for i, query in enumerate(self.search_queries):
                if not self.running:
                    break

                self.current_query_index = i

                # Aguardar se estiver pausado
                while self.paused and self.running:
                    time.sleep(1)

                self.status_updated.emit(f"[INFO] Executando busca {i+1}/{len(self.search_queries)}: {query}")
                self.progress_updated.emit(int((i / len(self.search_queries)) * 100))

                # Limpar busca anterior
                search_box = navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]')
                search_box.clear()
                time.sleep(1)

                # Inserir nova busca
                search_box.send_keys(query)
                time.sleep(1)
                navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
                time.sleep(10)

                # Extrair dados de negócios
                business_list = self._extract_business_data(navegador, query)

                # Salvar resultados
                if business_list.business_list:
                    self._save_results(business_list, query)

                # Sinalizar que a consulta foi concluída
                self.query_completed.emit(i)

                # Aguardar entre buscas
                time.sleep(5)

            self.status_updated.emit("[INFO] Todas as buscas foram concluídas.")
            self.progress_updated.emit(100)
            self.finished_signal.emit("Todas as buscas automatizadas foram concluídas com sucesso.")

        except Exception as e:
            error_msg = f"[ERRO] {str(e)}"
            self.status_updated.emit(error_msg)
            self.finished_signal.emit(error_msg)
        finally:
            try:
                navegador.quit()
            except:
                pass
            self.running = False

    def _extract_business_data(self, navegador, query, max_results=50):
        """Extrai dados de negócios do Google Maps."""
        business_list = BusinessList()
        i = 0
        move_count = 0
        max_moves = 4

        while i < max_results and move_count < max_moves and self.running:
            # Aguardar se estiver pausado
            while self.paused and self.running:
                time.sleep(1)

            previously_counted = 0
            stuck_count = 0

            # Número mínimo de elementos a encontrar antes de começar a processar
            # Isso ajuda a garantir que temos elementos suficientes para escolher
            min_elements_to_find = min(max_results * 2, 20)  # No máximo 20 elementos ou 2x o número solicitado

            # Rolar apenas até encontrar elementos suficientes ou ficar preso
            while self.running:
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                if not list_elem:
                    break

                current_count = len(list_elem)

                # Se já temos elementos suficientes, podemos parar de rolar
                if current_count >= min_elements_to_find:
                    self.status_updated.emit(f"[INFO] Encontrados {current_count} elementos, processando os primeiros {max_results}...")
                    break

                # Se já temos elementos suficientes para o que o usuário pediu, podemos parar de rolar
                if current_count >= max_results:
                    self.status_updated.emit(f"[INFO] Encontrados {current_count} elementos, processando...")
                    break

                action = ActionChains(navegador)
                try:
                    action.move_to_element(list_elem[-1]).perform()
                except Exception:
                    list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                    if list_elem:
                        action.move_to_element(list_elem[-1]).perform()
                time.sleep(3)  # Reduzido para 3 segundos

                scroll_origin = ScrollOrigin.from_element(list_elem[-1])
                action.scroll_from_origin(scroll_origin, 0, 1200).perform()
                time.sleep(5)  # Reduzido para 5 segundos
                action.scroll_from_origin(scroll_origin, 0, 250).perform()

                current_count = len(list_elem)
                if current_count == previously_counted:
                    stuck_count += 1
                    if stuck_count >= 2:  # Reduzido para 2 tentativas
                        self.status_updated.emit(f"[INFO] Não foi possível encontrar mais elementos, processando os {current_count} encontrados...")
                        break
                else:
                    stuck_count = 0
                    previously_counted = current_count

            # Processar os elementos encontrados
            list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            for element in list_elem[i:]:
                if i >= max_results or not self.running:
                    break

                try:
                    time.sleep(2)
                    navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(2)
                    try:
                        element.click()
                    except Exception as click_err:
                        self.status_updated.emit(f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}")
                        i += 1
                        continue
                    time.sleep(6)

                    # Extrair dados do negócio
                    business = self._extract_single_business(navegador)
                    if business:
                        business_list.business_list.append(business)

                    i += 1
                    self.status_updated.emit(f"[INFO] Extraído {i}/{max_results} negócios para '{query}'")

                    # Fechar o painel de detalhes do negócio
                    try:
                        close_button = navegador.find_element(By.XPATH, '//button[@aria-label="Voltar ao resultado da pesquisa"]')
                        close_button.click()
                        time.sleep(2)
                    except:
                        pass

                except Exception as e:
                    self.status_updated.emit(f"[ERRO] {str(e)}")
                    i += 1

            # Se não atingimos o número desejado de resultados, mover o mapa
            if i < max_results and self.running:
                directions = ['right', 'down', 'left', 'up']
                move_map(navegador, directions[move_count % len(directions)])
                move_count += 1
                time.sleep(5)

        return business_list

    def _extract_single_business(self, navegador):
        """Extrai dados de um único negócio."""
        try:
            # XPaths para extração dos dados
            name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
            address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
            website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
            phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'

            # Criar objeto Business
            business = Business()

            # Extrair dados
            try:
                business.name = navegador.find_element(By.XPATH, name_xpath).text
            except NoSuchElementException:
                business.name = "Não disponível"

            try:
                business.address = navegador.find_element(By.XPATH, address_xpath).text
            except NoSuchElementException:
                business.address = "Não disponível"

            try:
                business.website = navegador.find_element(By.XPATH, website_xpath).text
            except NoSuchElementException:
                business.website = "Não disponível"

            try:
                business.phone_number = navegador.find_element(By.XPATH, phone_number_xpath).text
            except NoSuchElementException:
                business.phone_number = "Não disponível"

            # Extrair avaliação e número de reviews
            try:
                rating_elem = navegador.find_element(By.CSS_SELECTOR, 'div.F7nice')
                rating_text = rating_elem.text
                if rating_text:
                    parts = rating_text.split('\n')
                    if len(parts) >= 2:
                        business.rating = parts[0]
                        business.reviews_count = parts[1].replace('(', '').replace(')', '')
            except NoSuchElementException:
                business.rating = "Não disponível"
                business.reviews_count = "Não disponível"

            return business

        except Exception as e:
            self.status_updated.emit(f"[ERRO] Falha ao extrair dados do negócio: {str(e)}")
            return None

    def _save_results(self, business_list, query):
        """Salva os resultados em arquivo."""
        try:
            # Criar um nome de arquivo seguro
            safe_query = query.replace(" ", "_").replace("/", "_").replace("\\", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automated_search_{safe_query}_{timestamp}"

            # Salvar em arquivo
            result = None
            if self.file_format.lower() == "excel":
                result = business_list.save_to_excel(filename, self.save_dir)
            elif self.file_format.lower() == "csv":
                result = business_list.save_to_csv(filename, self.save_dir)
            else:
                result = "Formato de arquivo inválido."

            self.status_updated.emit(result)

        except Exception as e:
            self.status_updated.emit(f"[ERRO] Falha ao salvar resultados: {str(e)}")

    def pause(self):
        """Pausa a execução das buscas."""
        self.paused = True

    def resume(self):
        """Retoma a execução das buscas."""
        self.paused = False

    def stop(self):
        """Para a execução das buscas."""
        self.running = False
