# message_templates.py
"""
Templates de mensagem para automação.
Edite os templates conforme necessário.
"""

# Template principal: recebe o descritor de tempo (se necessário) e o nome do negócio
txt = lambda time, business: f"Ol<PERSON>, poderia falar com {business}? Encontrei o contato no Google Maps e gostaria de agendar uma conversa."

# Lista de denominadores de tempo (se você quiser usar em outras partes ou adaptar o template)
time_var = ['Bom dia', 'Boa tarde', 'Boa noite']
