"""
Utility functions for detecting similarity between messages.
"""
from difflib import SequenceMatcher
import hashlib
import config

def calculate_hash(text):
    """
    Calculate SHA-256 hash of a text message.
    
    Args:
        text (str): The message text
        
    Returns:
        str: Hexadecimal hash string
    """
    return hashlib.sha256(text.encode('utf-8')).hexdigest()

def similaridade(a, b):
    """
    Calculate similarity ratio between two strings using SequenceMatcher.
    
    Args:
        a (str): First string
        b (str): Second string
        
    Returns:
        float: Similarity ratio between 0.0 and 1.0
    """
    return SequenceMatcher(None, a, b).ratio()

def is_similar_to_any(new_message, existing_messages, threshold=None):
    """
    Check if a new message is similar to any existing messages.
    
    Args:
        new_message (str): The new message to check
        existing_messages (list): List of existing messages to compare against
        threshold (float, optional): Similarity threshold. Defaults to config.SIMILARITY_THRESHOLD.
        
    Returns:
        bool: True if similar to any existing message, False otherwise
    """
    if threshold is None:
        threshold = config.SIMILARITY_THRESHOLD
        
    for message in existing_messages:
        if similaridade(new_message, message) >= threshold:
            return True
    return False

def pode_enviar(mensagem, mensagens_enviadas, limite_similaridade=None):
    """
    Check if a message can be sent based on similarity to previously sent messages.
    
    Args:
        mensagem (str): The message to check
        mensagens_enviadas (list): List of previously sent messages
        limite_similaridade (float, optional): Similarity threshold. Defaults to config.SIMILARITY_THRESHOLD.
        
    Returns:
        bool: True if the message can be sent, False otherwise
    """
    if limite_similaridade is None:
        limite_similaridade = config.SIMILARITY_THRESHOLD
        
    return not is_similar_to_any(mensagem, mensagens_enviadas, limite_similaridade)
