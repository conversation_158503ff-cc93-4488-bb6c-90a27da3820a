import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suprime a maioria das mensagens do TensorFlow

import flet as ft
import threading
import time
from tkinter import filedialog, Tk

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

from google_maps_scraper import main_query
from whats_invite_message import main as send_main, test_message, test_file

def select_directory():
    # Usando uma janela temporária para evitar problemas com threads
    root = Tk()
    root.attributes('-topmost', True)  # Mantém diálogo no topo
    root.withdraw()  # Esconde a janela principal
    try:
        directory = filedialog.askdirectory(parent=root)
    finally:
        root.destroy()  # Garante que a janela será destruída
    return directory

def select_file():
    # Usando uma janela temporária para evitar problemas com threads
    root = Tk()
    root.attributes('-topmost', True)  # Mantém diálogo no topo
    root.withdraw()  # Esconde a janela principal
    try:
        file_path = filedialog.askopenfilename(
            parent=root,
            filetypes=[("Excel files", "*.xlsx;*.xls")]
        )
    finally:
        root.destroy()  # Garante que a janela será destruída
    return file_path

def main(page: ft.Page):
    page.title = "Extração de Leads Google Maps"
    page.window_width = 800
    page.window_height = 900
    page.padding = 20
    page.theme_mode = "light"
    page.window_icon = "logo.ico"  # Adicionando o ícone da janela

    # Componentes da aba Google Maps
    business_name = ft.TextField(label="Nome/Termo", hint_text="Ex.: 'Restaurante'", width=300)
    location = ft.TextField(label="Localização", hint_text="Ex.: 'Rio de Janeiro'", width=300)
    total_results = ft.TextField(label="Total de Resultados", width=150, hint_text="Ex.: 50")
    save_dir = ft.TextField(label="Diretório para Salvar", width=300, read_only=True)
    file_format = ft.RadioGroup(
        content=ft.Row([
            ft.Radio(value="excel", label="Excel"),
            ft.Radio(value="csv", label="CSV")
        ])
    )
    file_format.value = "excel"
    status_text = ft.Text()
    progress_ring = ft.ProgressRing(visible=False)

    def select_save_dir(e):
        try:
            directory = select_directory()
            if directory:
                save_dir.value = directory
                save_dir.update()
        except Exception as ex:
            page.dialog = ft.AlertDialog(
                title=ft.Text("Erro"),
                content=ft.Text(f"Erro ao selecionar diretório: {str(ex)}"),
                actions=[ft.TextButton("OK", on_click=lambda _: setattr(page.dialog, 'open', False))]
            )
            page.dialog.open = True
            page.update()

    def show_continue_dialog():
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Continuar busca?"),
            content=ft.Text("Deseja continuar a busca movendo o mapa para encontrar mais resultados?"),
            actions=[
                ft.TextButton("Sim", on_click=lambda _: dialog.data.set("S")),
                ft.TextButton("Não", on_click=lambda _: dialog.data.set("N")),
            ],
        )
        
        async def show_dialog():
            dialog.data = ft.AsyncValue()
            page.dialog = dialog
            dialog.open = True
            page.update()
            result = await dialog.data.get()
            dialog.open = False
            page.update()
            return result
            
        return page.run_async(show_dialog)

    def scraper_callback(msg, require_response=False):
        if require_response:
            return show_continue_dialog()
        else:
            status_text.value = msg
            status_text.update()

    def run_google_query(e):
        if not all([business_name.value, location.value, total_results.value, save_dir.value]):
            page.show_snack_bar(ft.SnackBar(content=ft.Text("Por favor, preencha todos os campos")))
            return

        try:
            total = int(total_results.value)
        except ValueError:
            page.show_snack_bar(ft.SnackBar(content=ft.Text("Total de resultados deve ser um número")))
            return

        def run():
            progress_ring.visible = True
            progress_ring.update()
            search_for = f"{business_name.value} {location.value}"
            result = main_query(search_for, total, location.value, save_dir.value, file_format.value, callback=scraper_callback)
            progress_ring.visible = False
            progress_ring.update()
            page.show_snack_bar(ft.SnackBar(content=ft.Text(result)))

        threading.Thread(target=run, daemon=True).start()

    google_maps_tab = ft.Container(
        content=ft.Column([
            business_name,
            location,
            total_results,
            ft.Row([
                save_dir,
                ft.IconButton(
                    icon=ft.icons.FOLDER_OPEN,
                    tooltip="Selecionar diretório",
                    on_click=select_save_dir
                ),
            ]),
            ft.Text("Formato do arquivo:"),
            file_format,
            ft.Row([
                ft.ElevatedButton(
                    "Executar Consulta",
                    on_click=run_google_query
                ),
                progress_ring,
            ]),
            status_text,
        ], spacing=20),
        padding=20
    )

    # Componentes da aba Testar Mensagem
    phone_number = ft.TextField(
        label="Número para Teste",
        hint_text="Ex.: ***********",
        width=300
    )

    def run_test_message(e):
        if not phone_number.value:
            page.show_snack_bar(ft.SnackBar(content=ft.Text("Por favor, insira um número")))
            return

        def run():
            test_message()

        threading.Thread(target=run, daemon=True).start()

    test_message_tab = ft.Container(
        content=ft.Column([
            phone_number,
            ft.ElevatedButton(
                "Testar Mensagem",
                on_click=run_test_message
            )
        ], spacing=20),
        padding=20
    )

    # Componentes da aba Testar Arquivo
    def run_test_file(e):
        threading.Thread(target=test_file, daemon=True).start()

    test_file_tab = ft.Container(
        content=ft.Column([
            ft.ElevatedButton(
                "Testar Arquivo",
                on_click=run_test_file
            )
        ], spacing=20),
        padding=20
    )

    # Componentes da aba Time Loop
    time_loop_file_path = ft.TextField(label="Arquivo Excel", width=300, read_only=True)
    time_loop_hour = ft.TextField(label="Horário (HH:00)", hint_text="Ex.: 08:00", width=150)

    def select_time_loop_file(e):
        try:
            file_path = select_file()
            if file_path:
                time_loop_file_path.value = file_path
                time_loop_file_path.update()
        except Exception as ex:
            page.show_snack_bar(ft.SnackBar(content=ft.Text(f"Erro ao selecionar arquivo: {str(ex)}")))

    def run_time_loop(e):
        if not all([time_loop_file_path.value, time_loop_hour.value]):
            page.show_snack_bar(ft.SnackBar(content=ft.Text("Por favor, preencha todos os campos")))
            return

        def run():
            navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
            navegador.get("https://web.whatsapp.com/")
            while len(navegador.find_elements(By.ID, 'side')) < 1:
                time.sleep(1)

            while True:
                current = time.strftime("%H:%M")
                if current == time_loop_hour.value:
                    send_main(time_loop_file_path.value, navegador)
                    break
                time.sleep(60)

        threading.Thread(target=run, daemon=True).start()

    time_loop_tab = ft.Container(
        content=ft.Column([
            ft.Row([
                time_loop_file_path,
                ft.IconButton(
                    icon=ft.icons.FILE_OPEN,
                    tooltip="Selecionar arquivo",
                    on_click=select_time_loop_file
                ),
            ]),
            time_loop_hour,
            ft.ElevatedButton(
                "Iniciar Time Loop",
                on_click=run_time_loop
            ),
        ], spacing=20),
        padding=20
    )

    # Componentes da aba Enviar Agora
    send_now_file_path = ft.TextField(label="Arquivo Excel", width=300, read_only=True)

    def select_send_now_file(e):
        try:
            file_path = select_file()
            if file_path:
                send_now_file_path.value = file_path
                send_now_file_path.update()
        except Exception as ex:
            page.show_snack_bar(ft.SnackBar(content=ft.Text(f"Erro ao selecionar arquivo: {str(ex)}")))

    def run_send_now(e):
        if not send_now_file_path.value:
            page.show_snack_bar(ft.SnackBar(content=ft.Text("Por favor, selecione um arquivo")))
            return

        def run():
            navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
            navegador.get("https://web.whatsapp.com/")
            while len(navegador.find_elements(By.ID, 'side')) < 1:
                time.sleep(1)
            send_main(send_now_file_path.value, navegador)

        threading.Thread(target=run, daemon=True).start()

    send_now_tab = ft.Container(
        content=ft.Column([
            ft.Row([
                send_now_file_path,
                ft.IconButton(
                    icon=ft.icons.FILE_OPEN,
                    tooltip="Selecionar arquivo",
                    on_click=select_send_now_file
                ),
            ]),
            ft.ElevatedButton(
                "Enviar Agora",
                on_click=run_send_now
            ),
        ], spacing=20),
        padding=20
    )

    # Criando as abas
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="Consulta Google Maps",
                content=google_maps_tab,
            ),
            ft.Tab(
                text="Testar Mensagem",
                content=test_message_tab,
            ),
            ft.Tab(
                text="Testar Arquivo",
                content=test_file_tab,
            ),
            ft.Tab(
                text="Time Loop",
                content=time_loop_tab,
            ),
            ft.Tab(
                text="Enviar Agora",
                content=send_now_tab,
            ),
        ],
    )

    # Adicionando o logo
    try:
        logo = ft.Image(
            src="/logo.png",
            width=200,
            height=150,
            fit=ft.ImageFit.CONTAIN,
        )
    except Exception:
        logo = ft.Text(
            "ASIMOV TECH TECHNOLOGY",
            size=28,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER,
        )

    # Layout principal
    page.add(
        ft.Column([
            ft.Container(
                content=logo,
                alignment=ft.alignment.center,
                padding=20,
            ),
            ft.Divider(),
            tabs,
        ])
    )

if __name__ == "__main__":
    ft.app(target=main)
