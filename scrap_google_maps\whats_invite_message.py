import time
import urllib
import datetime
import threading
import os
import pyautogui as gui
import pandas as pd

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.common.exceptions import (NoSuchElementException, InvalidArgumentException, 
                                        UnexpectedAlertPresentException, TimeoutException)
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service

from message_templates import txt, time_var     # mantém o template original

DRIVER_PATH = 'chromedriver.exe'

# Função para limpar o terminal (usada em algumas partes do código original)
clear = lambda: os.system('cls' if os.name == 'nt' else 'clear')

# Abre o navegador e aguarda login no WhatsApp
def get_navegador():
    service = Service(executable_path=DRIVER_PATH)
    navegador = webdriver.Chrome(service=service)
    navegador.get("https://web.whatsapp.com/")
    while len(navegador.find_elements(By.ID, 'side')) < 1:
        time.sleep(1)
    return navegador

# Envia mídia (imagem) via WhatsApp
def enviar_midia(navegador, midia):
    navegador.find_element(By.XPATH, '//*[@id="main"]/footer/div[1]/div/span[2]/div/div[1]/div[2]/div/div/div/span').click()
    attach = navegador.find_element(By.CSS_SELECTOR, "input[type='file']")
    attach.send_keys(midia)
    time.sleep(3)
    send = navegador.find_element(By.CSS_SELECTOR, "span[data-icon='send']")
    send.click()

# Envia a mensagem (e anexa a mídia) para um número via WhatsApp Web
def send_message(navegador, numero, mensagem):
    max_retries = 1
    texto = urllib.parse.quote(mensagem)
    link = f"https://web.whatsapp.com/send?phone={numero}&text={texto}"
    
    if numero is None:
        return False, 'NONE_Number'
    try:
        for retry in range(1, max_retries + 1):
            try:
                navegador.get(link)
                WebDriverWait(navegador, timeout=50).until(EC.presence_of_element_located((By.ID, 'side')))
                time.sleep(10)
                break
            except TimeoutException:
                print(f"Timed out waiting for 'side'. Retry {retry} of {max_retries}...")
                if retry == max_retries:
                    return False, 'timeout_ERROR'
                time.sleep(10)
            except UnexpectedAlertPresentException:
                time.sleep(10)
                return False, 'Alert_text???'
        
        try:
            # Se encontrar o botão que indica "Número sem WhatsApp"
            navegador.find_element(By.XPATH, '//*[@id="app"]/div/span[2]/div/span/div/div/div/div/div/div[2]/div/button').click()
            time.sleep(5)
            return False, 'NO_WHATSAPP_Number'
        except NoSuchElementException:
            pass

        try:
            navegador.find_element(By.XPATH, '//*[@id="main"]/footer/div[1]/div/span[2]/div/div[2]/div[2]').click()
            time.sleep(5)
            enviar_midia(navegador, "images/2916315.png")  # referência à imagem
            time.sleep(7)
            return True, 'SUCCESS'
        except NoSuchElementException:
            time.sleep(5)
            return False, 'Element_ERROR'
        except InvalidArgumentException:
            time.sleep(5)
            return False, 'Image_ERROR'
    except Exception as e:
        time.sleep(10)
        return False, 'Ambiguious_general_ERROR'

# Retorna a saudação conforme o horário local (usando time_var do messages.py)
def get_time_local():
    currentTime = int(time.strftime('%H'))
    if currentTime < 12:
        return time_var[0]
    if currentTime >= 12:
        return time_var[1]
    if currentTime > 6:
        return time_var[2]

# Função principal que lê o arquivo Excel e envia mensagens para cada contato
def main(excel_file, navegador, callback=None):
    df = pd.read_excel(excel_file)
    total_rows = df.shape[0]
    errors = []
    succ_int = 0
    start_ = time.perf_counter()
    
    for index, row in df.iterrows():
        time_now = datetime.datetime.now().strftime("%H:%M")
        buss_name = row['name']
        buss_number = row['phone_number']
        try:
            conditional_time = get_time_local()
        except:
            conditional_time = time_var[0]
        mensagem = txt(conditional_time, buss_name)
        buss_number = str('55' + str(buss_number))
        send = send_message(navegador, buss_number, mensagem)
        print(f"""
Runned at {time_now}, Nº: {index}/{total_rows - 1}
Name: {buss_name}
LOG: {send}
-----------------------------------------------""")
        if send[0] is False:
            errors.append({'Name': buss_name, 'Error': send[1]})
        else:
            succ_int += 1

    clear()
    finish_ = time.perf_counter()
    final_time = round(int(finish_ - start_) / 60)
    result = f'''
Done Running! {time_now}, runned in {final_time} minutes
Successfull messages = {succ_int}/{total_rows - 1}
Errors = {len(errors)}/{total_rows - 1}'''
    
    details = "\n".join([f"{err['Name']}: {err['Error']}" for err in errors])
    
    if callback:
        callback(result, details)
    return result, details

# Função para testar o envio de mensagem para um único número
def test_message(number=None, callback=None):
    if not number:
        return "Número não fornecido", None
    
    number = str('55' + number.strip())
    start_ = time.perf_counter()
    try:
        conditional_time = get_time_local()
    except:
        conditional_time = time_var[0]
    mensagem = txt(conditional_time, "TESTE")
    print("Abrindo navegador...")
    navegador = get_navegador()
    send = send_message(navegador, number, mensagem)
    finish_ = time.perf_counter()
    final_time = round(int(finish_ - start_) / 60)
    
    if send[0] is False:
        result = f"Falha - razão: {send[1]}\nTempo: {final_time} minutos"
    else:
        result = f"Sucesso!\nTempo: {final_time} minutos"
    
    if callback:
        callback(result)
    return result

# Função para testar a leitura de arquivo e exibir conteúdo básico
def test_file(file_path=None, callback=None):
    if not file_path:
        return "Arquivo não fornecido", None
        
    df = pd.read_excel(file_path)
    total_rows = df.shape[0]
    details = ""
    for index, row in df.iterrows():
        time_now = datetime.datetime.now().strftime("%H:%M")
        buss_name = row.get('name', '')
        buss_address = row.get('address', '')
        buss_number = row.get('phone_number', '')
        buss_web_site = row.get('website', '')
        details += (f"\nRunned at {time_now}, Nº: {index}/{total_rows - 1}\n"
                    f"Name: {buss_name}\nAddress: {buss_address}\nNumber: {buss_number}\nWebsite: {buss_web_site}\n"
                    "------------------------------------------------\n")
    
    if callback:
        callback(details)
    return details
