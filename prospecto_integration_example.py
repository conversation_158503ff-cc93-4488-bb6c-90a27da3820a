"""
Example of how to integrate the message generation system with PROSPECTO.
"""
import json
import subprocess
import pandas as pd
import os
from pathlib import Path

def generate_message_variations(example_message, count=3):
    """
    Generate variations of an example message.
    
    Args:
        example_message (str): The example message
        count (int, optional): Number of variations to generate. Defaults to 3.
        
    Returns:
        list: List of generated message variations
    """
    try:
        # Call the message generation script
        cmd = ["python", "gerar_mensagem.py", "--example", example_message, "--count", str(count)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating messages: {result.stderr}")
            return []
        
        # Parse the output to extract the generated messages
        # This is a simple approach; you might need to adjust based on actual output format
        variations = []
        for line in result.stdout.split("\n"):
            if line.startswith("---"):
                continue
            if "Variation" in line:
                continue
            if line.strip() and not line.startswith(("Generated", "Saved")):
                variations.append(line.strip())
        
        return variations
    
    except Exception as e:
        print(f"Error: {e}")
        return []

def get_next_unused_message():
    """
    Get the next unused message from the database.
    
    Returns:
        dict: Message dictionary or None if no unused messages are available
    """
    try:
        # Call the message sending script to get an unused message
        cmd = ["python", "enviar_mensagem.py", "--action", "get"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error getting message: {result.stderr}")
            return None
        
        # Parse the output as JSON
        output = result.stdout.strip()
        if not output or "No unused messages available" in output:
            return None
        
        return json.loads(output)
    
    except Exception as e:
        print(f"Error: {e}")
        return None

def mark_message_as_sent(message_id, conversion=False):
    """
    Mark a message as sent.
    
    Args:
        message_id (int): The message ID
        conversion (bool, optional): Whether the message resulted in a conversion. Defaults to False.
        
    Returns:
        bool: True if marked successfully, False otherwise
    """
    try:
        # Build the command
        cmd = ["python", "enviar_mensagem.py", "--action", "mark", "--id", str(message_id)]
        if conversion:
            cmd.append("--conversion")
        
        # Call the message sending script to mark the message as sent
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error marking message: {result.stderr}")
            return False
        
        return "marked as sent successfully" in result.stdout
    
    except Exception as e:
        print(f"Error: {e}")
        return False

def enhance_prospecto_excel(excel_path, output_path=None):
    """
    Enhance a PROSPECTO Excel file with generated message variations.
    
    Args:
        excel_path (str): Path to the Excel file
        output_path (str, optional): Path to save the enhanced Excel file. Defaults to None.
        
    Returns:
        str: Path to the enhanced Excel file
    """
    try:
        # Load the Excel file
        df = pd.read_excel(excel_path)
        
        # Check if the required columns exist
        if "telefone" not in df.columns or "mensagem" not in df.columns:
            print("Error: Excel file must contain 'telefone' and 'mensagem' columns.")
            return None
        
        # Create a copy of the dataframe
        enhanced_df = df.copy()
        
        # Get unique messages
        unique_messages = df["mensagem"].unique()
        
        # Generate variations for each unique message
        variations_map = {}
        for message in unique_messages:
            variations = generate_message_variations(message, count=3)
            if variations:
                variations_map[message] = variations
        
        # Replace messages with variations where possible
        for i, row in enhanced_df.iterrows():
            original_message = row["mensagem"]
            if original_message in variations_map and variations_map[original_message]:
                # Use a variation and remove it from the list
                variation = variations_map[original_message].pop(0)
                enhanced_df.at[i, "mensagem"] = variation
                
                # If we've used all variations, generate more
                if not variations_map[original_message]:
                    new_variations = generate_message_variations(original_message, count=3)
                    if new_variations:
                        variations_map[original_message] = new_variations
        
        # Save the enhanced Excel file
        if output_path is None:
            base_path = Path(excel_path)
            output_path = base_path.parent / f"{base_path.stem}_enhanced{base_path.suffix}"
        
        enhanced_df.to_excel(output_path, index=False)
        print(f"Enhanced Excel file saved to {output_path}")
        
        return str(output_path)
    
    except Exception as e:
        print(f"Error: {e}")
        return None

def example_usage():
    """Example of how to use the integration functions."""
    # Example 1: Generate variations of a message
    example_message = "Olá, tudo bem? Me chamo Claudio, sou contador e especializado em clínicas odontológicas em Madureira. Tenho ajudado consultórios a economizar impostos e manter a regularidade fiscal. Podemos conversar rapidamente por aqui?"
    variations = generate_message_variations(example_message, count=3)
    
    print("Generated variations:")
    for i, variation in enumerate(variations, 1):
        print(f"{i}. {variation}")
    
    # Example 2: Get an unused message
    message = get_next_unused_message()
    if message:
        print(f"\nNext unused message (ID: {message['id']}):")
        print(message['message'])
        
        # Mark as sent
        mark_message_as_sent(message['id'])
        print(f"Message {message['id']} marked as sent.")
    else:
        print("\nNo unused messages available.")
    
    # Example 3: Enhance a PROSPECTO Excel file
    # Uncomment to use
    # excel_path = "path/to/your/prospecto_contacts.xlsx"
    # enhanced_path = enhance_prospecto_excel(excel_path)
    # if enhanced_path:
    #     print(f"\nEnhanced Excel file: {enhanced_path}")

if __name__ == "__main__":
    example_usage()
