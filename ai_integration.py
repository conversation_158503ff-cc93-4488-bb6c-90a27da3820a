"""
Módulo de integração com APIs de IA para o aplicativo PROSPECTO.
Fornece funções para interagir com diferentes provedores de IA.
"""
import json
import requests
from typing import List, Dict, Any, Optional

import config_manager

def get_search_suggestions(partial_query: str, num_suggestions: int = 5) -> List[str]:
    """
    Obtém sugestões de busca com base em uma consulta parcial.
    
    Args:
        partial_query: A consulta parcial digitada pelo usuário
        num_suggestions: Número de sugestões a retornar
        
    Returns:
        Lista de sugestões de busca
    """
    config = config_manager.load_config()
    provider = config.get("ai_provider", "OpenAI")
    model = config.get("ai_model", "GPT-4 Mini")
    api_key = config.get("ai_api_key", "")
    
    # Se não houver chave de API, retornar sugestões padrão
    if not api_key:
        return _get_default_search_suggestions(partial_query, num_suggestions)
    
    try:
        if provider == "OpenAI":
            return _get_openai_suggestions(partial_query, num_suggestions, model, api_key)
        elif provider == "Anthropic":
            return _get_anthropic_suggestions(partial_query, num_suggestions, model, api_key)
        elif provider == "Google":
            return _get_google_suggestions(partial_query, num_suggestions, model, api_key)
        else:
            return _get_default_search_suggestions(partial_query, num_suggestions)
    except Exception as e:
        print(f"Erro ao obter sugestões de IA: {e}")
        return _get_default_search_suggestions(partial_query, num_suggestions)

def get_profession_suggestions(num_suggestions: int = 5) -> List[str]:
    """
    Obtém sugestões de profissões para busca.
    
    Args:
        num_suggestions: Número de sugestões a retornar
        
    Returns:
        Lista de sugestões de profissões
    """
    config = config_manager.load_config()
    provider = config.get("ai_provider", "OpenAI")
    model = config.get("ai_model", "GPT-4 Mini")
    api_key = config.get("ai_api_key", "")
    
    # Se não houver chave de API, retornar sugestões padrão
    if not api_key:
        return _get_default_profession_suggestions(num_suggestions)
    
    try:
        if provider == "OpenAI":
            return _get_openai_professions(num_suggestions, model, api_key)
        elif provider == "Anthropic":
            return _get_anthropic_professions(num_suggestions, model, api_key)
        elif provider == "Google":
            return _get_google_professions(num_suggestions, model, api_key)
        else:
            return _get_default_profession_suggestions(num_suggestions)
    except Exception as e:
        print(f"Erro ao obter sugestões de profissões: {e}")
        return _get_default_profession_suggestions(num_suggestions)

def get_location_suggestions(num_suggestions: int = 5) -> List[str]:
    """
    Obtém sugestões de localizações para busca.
    
    Args:
        num_suggestions: Número de sugestões a retornar
        
    Returns:
        Lista de sugestões de localizações
    """
    config = config_manager.load_config()
    provider = config.get("ai_provider", "OpenAI")
    model = config.get("ai_model", "GPT-4 Mini")
    api_key = config.get("ai_api_key", "")
    
    # Se não houver chave de API, retornar sugestões padrão
    if not api_key:
        return _get_default_location_suggestions(num_suggestions)
    
    try:
        if provider == "OpenAI":
            return _get_openai_locations(num_suggestions, model, api_key)
        elif provider == "Anthropic":
            return _get_anthropic_locations(num_suggestions, model, api_key)
        elif provider == "Google":
            return _get_google_locations(num_suggestions, model, api_key)
        else:
            return _get_default_location_suggestions(num_suggestions)
    except Exception as e:
        print(f"Erro ao obter sugestões de localizações: {e}")
        return _get_default_location_suggestions(num_suggestions)

# Funções auxiliares para obter sugestões padrão
def _get_default_search_suggestions(partial_query: str, num_suggestions: int) -> List[str]:
    """Retorna sugestões de busca padrão."""
    suggestions = [
        "DENTISTA + RJ",
        "ADVOGADO + SP",
        "MÉDICO + MG",
        "CONTADOR + RS",
        "ENGENHEIRO + PR",
        "ARQUITETO + SC",
        "PSICÓLOGO + BA",
        "NUTRICIONISTA + PE",
        "FISIOTERAPEUTA + CE",
        "PROFESSOR + DF"
    ]
    
    # Filtrar sugestões com base na consulta parcial
    if partial_query:
        filtered = [s for s in suggestions if partial_query.lower() in s.lower()]
        return filtered[:num_suggestions] if filtered else suggestions[:num_suggestions]
    
    return suggestions[:num_suggestions]

def _get_default_profession_suggestions(num_suggestions: int) -> List[str]:
    """Retorna sugestões de profissões padrão."""
    suggestions = [
        "ADVOGADO",
        "MÉDICO",
        "DENTISTA",
        "CONTADOR",
        "ENGENHEIRO",
        "ARQUITETO",
        "PSICÓLOGO",
        "NUTRICIONISTA",
        "FISIOTERAPEUTA",
        "PROFESSOR"
    ]
    return suggestions[:num_suggestions]

def _get_default_location_suggestions(num_suggestions: int) -> List[str]:
    """Retorna sugestões de localizações padrão."""
    suggestions = [
        "São Paulo, SP",
        "Rio de Janeiro, RJ",
        "Belo Horizonte, MG",
        "Porto Alegre, RS",
        "Curitiba, PR",
        "Florianópolis, SC",
        "Salvador, BA",
        "Recife, PE",
        "Fortaleza, CE",
        "Brasília, DF"
    ]
    return suggestions[:num_suggestions]

# Implementações específicas para cada provedor de IA
def _get_openai_suggestions(query: str, num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões usando a API da OpenAI."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_search_suggestions(query, num)

def _get_anthropic_suggestions(query: str, num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões usando a API da Anthropic."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_search_suggestions(query, num)

def _get_google_suggestions(query: str, num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões usando a API do Google."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_search_suggestions(query, num)

def _get_openai_professions(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de profissões usando a API da OpenAI."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_profession_suggestions(num)

def _get_anthropic_professions(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de profissões usando a API da Anthropic."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_profession_suggestions(num)

def _get_google_professions(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de profissões usando a API do Google."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_profession_suggestions(num)

def _get_openai_locations(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de localizações usando a API da OpenAI."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_location_suggestions(num)

def _get_anthropic_locations(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de localizações usando a API da Anthropic."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_location_suggestions(num)

def _get_google_locations(num: int, model: str, api_key: str) -> List[str]:
    """Obtém sugestões de localizações usando a API do Google."""
    # Implementação básica - em um ambiente real, seria mais completa
    return _get_default_location_suggestions(num)
