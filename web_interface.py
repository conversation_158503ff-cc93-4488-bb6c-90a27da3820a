import flet as ft
import threading
import time
import os
from tkinter import filedialog, Tk
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Import existing functionality
from google_maps_scraper import main_query
from whats_invite_message import main as send_main, test_message, test_file

# Import new functionality
from automated_maps_search import AutomatedMapSearch
from gpt_autocomplete import get_search_suggestions, get_profession_suggestions, get_location_suggestions

# Import theme and styles
from theme import (
    apply_text_style, create_card, create_button, create_text_field,
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACKGROUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    PADDING_SMALL, PADDING_MEDIUM, PADDING_LARGE, SHADOW_MEDIUM
)

def create_interface(page: ft.Page):
    """
    Create the main interface with three tabs.

    Args:
        page (ft.Page): The Flet page object
    """
    # Status text for feedback
    status_text = ft.Text("Pronto para iniciar", size=14)

    # Progress indicator
    progress_ring = ft.ProgressRing(width=20, height=20, visible=False)

    # Helper function to select a directory
    def select_directory():
        root = Tk()
        root.withdraw()
        root.attributes("-topmost", True)
        directory = filedialog.askdirectory()
        root.destroy()
        return directory

    # Helper function to select a file
    def select_file():
        root = Tk()
        root.withdraw()
        root.attributes("-topmost", True)
        file_path = filedialog.askopenfilename()
        root.destroy()
        return file_path

    # Callback for scraper status updates
    def scraper_callback(msg, require_response=False):
        status_text.value = msg
        status_text.update()
        if require_response:
            # This is a simplified version - in a real app, you'd need a dialog
            return "S"  # Auto-continue for now

    # ===== TAB 1: MESSAGING =====

    # Components for the messaging tab
    phone_number = create_text_field(
        label="Número para Mensagem",
        hint_text="Ex.: 11955555555",
        width=400
    )

    message_text = create_text_field(
        label="Mensagem",
        hint_text="Digite sua mensagem aqui...",
        width=None,  # Full width
        multiline=True
    )
    message_text.min_lines = 5
    message_text.max_lines = 8

    # Status indicator for messaging
    messaging_status = ft.Text("Pronto para enviar mensagens", size=14, color=TEXT_SECONDARY)

    def send_message(e):
        if not phone_number.value:
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Por favor, insira um número de telefone"),
                bgcolor=ERROR_COLOR
            ))
            return

        if not message_text.value:
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Por favor, insira uma mensagem"),
                bgcolor=ERROR_COLOR
            ))
            return

        def run():
            messaging_status.value = f"Enviando mensagem para {phone_number.value}..."
            messaging_status.color = INFO_COLOR
            messaging_status.update()

            # Here you would integrate with your messaging system
            # For now, just simulate sending
            time.sleep(2)

            messaging_status.value = f"✓ Mensagem enviada para {phone_number.value}"
            messaging_status.color = SUCCESS_COLOR
            messaging_status.update()

            # Show success notification
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text(f"Mensagem enviada com sucesso para {phone_number.value}"),
                bgcolor=SUCCESS_COLOR
            ))

        threading.Thread(target=run, daemon=True).start()

    # Create a card for message templates
    templates_title = ft.Text("Templates de Mensagem", size=16, weight=ft.FontWeight.BOLD)

    templates_list = ft.ListView(
        controls=[
            ft.ListTile(
                leading=ft.Icon(ft.icons.MESSAGE, color=PRIMARY_COLOR),
                title=ft.Text("Apresentação Padrão"),
                subtitle=ft.Text("Olá, sou representante da empresa..."),
                trailing=ft.IconButton(icon=ft.icons.CONTENT_COPY, tooltip="Copiar"),
                on_click=lambda e: message_text.focus()
            ),
            ft.ListTile(
                leading=ft.Icon(ft.icons.MESSAGE, color=PRIMARY_COLOR),
                title=ft.Text("Proposta Comercial"),
                subtitle=ft.Text("Gostaria de apresentar nossa proposta..."),
                trailing=ft.IconButton(icon=ft.icons.CONTENT_COPY, tooltip="Copiar"),
                on_click=lambda e: message_text.focus()
            ),
            ft.ListTile(
                leading=ft.Icon(ft.icons.MESSAGE, color=PRIMARY_COLOR),
                title=ft.Text("Agendamento"),
                subtitle=ft.Text("Podemos agendar uma reunião para..."),
                trailing=ft.IconButton(icon=ft.icons.CONTENT_COPY, tooltip="Copiar"),
                on_click=lambda e: message_text.focus()
            ),
        ],
        spacing=2,
        padding=10,
        height=200,
    )

    templates_card = create_card(
        content=ft.Column([
            templates_title,
            ft.Divider(),
            templates_list,
        ], spacing=10),
        padding=15
    )

    # Create a card for the message form
    message_form_title = ft.Text("Nova Mensagem", size=16, weight=ft.FontWeight.BOLD)

    send_button = create_button(
        text="Enviar Mensagem",
        icon=ft.icons.SEND,
        on_click=send_message,
        style="primary"
    )

    clear_button = create_button(
        text="Limpar",
        icon=ft.icons.CLEAR,
        on_click=lambda e: (setattr(message_text, "value", ""), message_text.update()),
        style="secondary"
    )

    message_form_card = create_card(
        content=ft.Column([
            message_form_title,
            ft.Divider(),
            phone_number,
            message_text,
            ft.Row([
                send_button,
                clear_button,
            ], alignment=ft.MainAxisAlignment.END),
            messaging_status,
        ], spacing=15),
        padding=20
    )

    # Layout for messaging tab
    messaging_tab = ft.Container(
        content=ft.Column([
            # Title and description
            ft.Container(
                content=ft.Column([
                    ft.Text("Mensagens", size=20, weight=ft.FontWeight.BOLD, color=PRIMARY_COLOR),
                    ft.Text("Envie mensagens personalizadas para seus leads.", color=TEXT_SECONDARY),
                ], spacing=5),
                margin=ft.margin.only(bottom=20)
            ),

            # Two-column layout
            ft.ResponsiveRow([
                # Left column - Message form
                ft.Column([
                    message_form_card,
                ], col={"sm": 12, "md": 8}, spacing=20),

                # Right column - Templates
                ft.Column([
                    templates_card,
                ], col={"sm": 12, "md": 4}, spacing=20),
            ]),
        ], spacing=20),
        padding=20
    )

    # ===== TAB 2: GOOGLE MAPS =====

    # Components for the Google Maps tab (reusing existing functionality)
    business_name = create_text_field(
        label="Nome/Termo",
        hint_text="Ex.: 'Restaurante'",
        width=400
    )

    location = create_text_field(
        label="Localização",
        hint_text="Ex.: 'Rio de Janeiro'",
        width=400
    )

    total_results = create_text_field(
        label="Total de Resultados",
        hint_text="Ex.: 50",
        width=200
    )

    save_dir = create_text_field(
        label="Diretório para Salvar",
        width=400,
        read_only=True
    )

    # File format selection with better styling
    file_format_title = ft.Text("Formato do arquivo:", size=14, color=TEXT_SECONDARY)

    file_format = ft.RadioGroup(
        content=ft.Row([
            ft.Radio(value="excel", label="Excel"),
            ft.Radio(value="csv", label="CSV")
        ])
    )

    # Status indicator for Google Maps
    maps_status = ft.Text("Pronto para buscar", size=14, color=TEXT_SECONDARY)

    # Progress indicator
    maps_progress = ft.ProgressRing(width=24, height=24, visible=False, color=PRIMARY_COLOR)

    def select_save_dir(e):
        directory = select_directory()
        if directory:
            save_dir.value = directory
            save_dir.update()

    def run_google_query(e):
        if not all([business_name.value, location.value, total_results.value, save_dir.value]):
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Por favor, preencha todos os campos"),
                bgcolor=ERROR_COLOR
            ))
            return

        try:
            total = int(total_results.value)
        except ValueError:
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Total de resultados deve ser um número"),
                bgcolor=ERROR_COLOR
            ))
            return

        def run():
            maps_progress.visible = True
            maps_status.value = "Buscando no Google Maps..."
            maps_status.color = INFO_COLOR
            maps_progress.update()
            maps_status.update()

            search_for = f"{business_name.value} {location.value}"
            result = main_query(search_for, total, location.value, save_dir.value, file_format.value, callback=scraper_callback)

            maps_progress.visible = False

            if "ERRO" in result:
                maps_status.value = "Erro na busca. Verifique os parâmetros."
                maps_status.color = ERROR_COLOR
                page.show_snack_bar(ft.SnackBar(
                    content=ft.Text(result),
                    bgcolor=ERROR_COLOR
                ))
            else:
                maps_status.value = "Busca concluída com sucesso!"
                maps_status.color = SUCCESS_COLOR
                page.show_snack_bar(ft.SnackBar(
                    content=ft.Text(result),
                    bgcolor=SUCCESS_COLOR
                ))

            maps_progress.update()
            maps_status.update()

        threading.Thread(target=run, daemon=True).start()

    # Create search button with better styling
    search_button = create_button(
        text="Executar Consulta",
        icon=ft.icons.SEARCH,
        on_click=run_google_query,
        style="primary"
    )

    # Create a card for search form
    search_form_title = ft.Text("Parâmetros de Busca", size=16, weight=ft.FontWeight.BOLD)

    search_form_card = create_card(
        content=ft.Column([
            search_form_title,
            ft.Divider(),
            ft.Row([
                ft.Column([
                    business_name,
                    location,
                ], spacing=15),
                ft.Column([
                    total_results,
                    ft.Container(
                        content=ft.Column([
                            file_format_title,
                            file_format,
                        ], spacing=5),
                    ),
                ], spacing=15),
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            ft.Row([
                save_dir,
                ft.IconButton(
                    icon=ft.icons.FOLDER_OPEN,
                    tooltip="Selecionar diretório",
                    on_click=select_save_dir
                ),
            ]),
            ft.Row([
                search_button,
                maps_progress,
            ], alignment=ft.MainAxisAlignment.END),
            maps_status,
        ], spacing=15),
        padding=20
    )

    # Create a card for search results preview
    results_preview_title = ft.Text("Prévia dos Resultados", size=16, weight=ft.FontWeight.BOLD)

    # Sample results data (would be populated from actual search)
    results_preview = ft.DataTable(
        columns=[
            ft.DataColumn(ft.Text("Nome")),
            ft.DataColumn(ft.Text("Endereço")),
            ft.DataColumn(ft.Text("Telefone")),
            ft.DataColumn(ft.Text("Website")),
        ],
        rows=[
            ft.DataRow(
                cells=[
                    ft.DataCell(ft.Text("Os resultados da busca")),
                    ft.DataCell(ft.Text("aparecerão aqui")),
                    ft.DataCell(ft.Text("")),
                    ft.DataCell(ft.Text("")),
                ],
            ),
        ],
    )

    results_preview_card = create_card(
        content=ft.Column([
            results_preview_title,
            ft.Divider(),
            results_preview,
        ], spacing=10),
        padding=15
    )

    # Layout for Google Maps tab
    google_maps_tab = ft.Container(
        content=ft.Column([
            # Title and description
            ft.Container(
                content=ft.Column([
                    ft.Text("Google Maps", size=20, weight=ft.FontWeight.BOLD, color=PRIMARY_COLOR),
                    ft.Text("Busque e extraia dados de empresas e profissionais do Google Maps.", color=TEXT_SECONDARY),
                ], spacing=5),
                margin=ft.margin.only(bottom=20)
            ),

            # Search form
            search_form_card,

            # Results preview
            results_preview_card,
        ], spacing=20),
        padding=20
    )

    # ===== TAB 3: AUTOMATED GOOGLE MAPS SEARCH =====

    # Components for the automated search tab
    search_queries = []
    search_query_fields = []
    automated_search_instance = None

    # Create a container for search query fields
    search_queries_container = ft.Column(spacing=10)

    # Function to add a search query field
    def add_search_query_field(query=""):
        # Create a row with a text field and suggestion button
        query_field = create_text_field(
            label=f"Busca {len(search_query_fields) + 1}",
            hint_text="Ex.: 'DENTISTA + RJ'",
            width=400
        )
        query_field.value = query

        # Function to get suggestions
        def get_suggestions(e):
            if not query_field.value:
                suggestions = get_profession_suggestions(5)
                locations = get_location_suggestions(5)
                # Combine professions and locations
                combined_suggestions = [f"{prof} + {loc}" for prof, loc in zip(suggestions[:5], locations[:5])]
            else:
                combined_suggestions = get_search_suggestions(query_field.value, 5)

            # Show suggestions dialog
            def use_suggestion(e, suggestion):
                query_field.value = suggestion
                query_field.update()
                suggestion_dialog.open = False
                page.update()

            suggestion_items = [
                ft.ListTile(
                    leading=ft.Icon(ft.icons.SEARCH, color=PRIMARY_COLOR),
                    title=ft.Text(suggestion),
                    on_click=lambda e, s=suggestion: use_suggestion(e, s)
                ) for suggestion in combined_suggestions
            ]

            suggestion_dialog = ft.AlertDialog(
                title=ft.Text("Sugestões de Busca"),
                content=ft.Column([
                    ft.Text("Clique em uma sugestão para usá-la:"),
                    ft.Divider(),
                    *suggestion_items
                ], scroll=ft.ScrollMode.AUTO, height=300),
                actions=[
                    ft.TextButton("Fechar", on_click=lambda e: setattr(suggestion_dialog, "open", False))
                ]
            )

            page.dialog = suggestion_dialog
            suggestion_dialog.open = True
            page.update()

        # Create suggestion button
        suggestion_button = ft.IconButton(
            icon=ft.icons.LIGHTBULB_OUTLINE,
            icon_color=ACCENT_COLOR,
            tooltip="Obter sugestões com GPT-4 Mini",
            on_click=get_suggestions
        )

        # Create remove button
        def remove_field(e):
            index = search_query_fields.index(query_field)
            search_queries_container.controls.pop(index)
            search_query_fields.pop(index)
            # Update labels
            for i, field in enumerate(search_query_fields):
                field.label = f"Busca {i + 1}"
            search_queries_container.update()

        remove_button = ft.IconButton(
            icon=ft.icons.DELETE_OUTLINE,
            icon_color=ERROR_COLOR,
            tooltip="Remover",
            on_click=remove_field
        )

        # Add to container
        search_queries_container.controls.append(
            ft.Container(
                content=ft.Row([
                    query_field,
                    suggestion_button,
                    remove_button
                ]),
                border_radius=8,
                padding=5,
                margin=5,
            )
        )
        search_query_fields.append(query_field)
        search_queries_container.update()

    # Add initial search query fields (17 as requested)
    for i in range(17):
        add_search_query_field()

    # Directory selection for saving results
    automated_save_dir = create_text_field(
        label="Diretório para Salvar",
        width=400,
        read_only=True
    )

    def select_automated_save_dir(e):
        directory = select_directory()
        if directory:
            automated_save_dir.value = directory
            automated_save_dir.update()

    # File format selection with better styling
    automated_file_format_title = ft.Text("Formato do arquivo:", size=14, color=TEXT_SECONDARY)

    automated_file_format = ft.RadioGroup(
        content=ft.Row([
            ft.Radio(value="excel", label="Excel"),
            ft.Radio(value="csv", label="CSV")
        ])
    )

    # Status indicator for automated search
    automated_status = ft.Text("Pronto para iniciar buscas automatizadas", size=14, color=TEXT_SECONDARY)

    # Progress indicator
    automated_progress = ft.ProgressRing(width=24, height=24, visible=False, color=PRIMARY_COLOR)

    # Control buttons with better styling
    start_button = create_button(
        text="Iniciar Buscas Automatizadas",
        icon=ft.icons.PLAY_ARROW,
        on_click=lambda e: start_automated_search(),
        style="primary"
    )

    pause_button = create_button(
        text="Pausar",
        icon=ft.icons.PAUSE,
        on_click=lambda e: pause_automated_search(),
        style="secondary",
        disabled=True
    )

    stop_button = create_button(
        text="Parar",
        icon=ft.icons.STOP,
        on_click=lambda e: stop_automated_search(),
        style="error",
        disabled=True
    )

    # Function to start automated search
    def start_automated_search():
        global automated_search_instance

        # Validate inputs
        if not automated_save_dir.value:
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Por favor, selecione um diretório para salvar os resultados"),
                bgcolor=ERROR_COLOR
            ))
            return

        # Get search queries
        queries = [field.value for field in search_query_fields if field.value]
        if not queries:
            page.show_snack_bar(ft.SnackBar(
                content=ft.Text("Por favor, adicione pelo menos uma consulta de busca"),
                bgcolor=ERROR_COLOR
            ))
            return

        # Update UI
        automated_progress.visible = True
        automated_status.value = "Iniciando buscas automatizadas..."
        automated_status.color = INFO_COLOR
        start_button.disabled = True
        pause_button.disabled = False
        stop_button.disabled = False
        page.update()

        # Callback for status updates
        def search_callback(msg, require_response=False):
            automated_status.value = msg
            automated_status.update()
            if require_response:
                return "S"  # Auto-continue for now

        # Create and start automated search
        automated_search_instance = AutomatedMapSearch(
            search_queries=queries,
            save_dir=automated_save_dir.value,
            file_format=automated_file_format.value or "excel",
            callback=search_callback
        )
        automated_search_instance.start()

    # Function to pause automated search
    def pause_automated_search():
        global automated_search_instance
        if automated_search_instance:
            if automated_search_instance.paused:
                automated_search_instance.resume()
                pause_button.text = "Pausar"
                pause_button.icon = ft.icons.PAUSE
                automated_status.value = "Buscas automatizadas retomadas"
                automated_status.color = INFO_COLOR
            else:
                automated_search_instance.pause()
                pause_button.text = "Continuar"
                pause_button.icon = ft.icons.PLAY_ARROW
                automated_status.value = "Buscas automatizadas pausadas"
                automated_status.color = WARNING_COLOR
            page.update()

    # Function to stop automated search
    def stop_automated_search():
        global automated_search_instance
        if automated_search_instance:
            automated_search_instance.stop()
            automated_search_instance = None

        # Update UI
        automated_progress.visible = False
        automated_status.value = "Buscas automatizadas interrompidas"
        automated_status.color = ERROR_COLOR
        start_button.disabled = False
        pause_button.disabled = True
        stop_button.disabled = True
        pause_button.text = "Pausar"
        pause_button.icon = ft.icons.PAUSE
        page.update()

    # Create a card for search queries
    search_queries_title = ft.Text("Consultas de Busca", size=16, weight=ft.FontWeight.BOLD)

    # Add button for adding new queries
    add_query_button = ft.FloatingActionButton(
        icon=ft.icons.ADD,
        bgcolor=PRIMARY_COLOR,
        mini=True,
        tooltip="Adicionar nova consulta",
        on_click=lambda e: add_search_query_field()
    )

    search_queries_card = create_card(
        content=ft.Column([
            ft.Row([
                search_queries_title,
                ft.Container(width=10),  # Spacer
                add_query_button,
            ], alignment=ft.MainAxisAlignment.START),
            ft.Divider(),
            ft.Container(
                content=search_queries_container,
                height=400,
                border_radius=8,
                padding=10,
                expand=True,
                scroll=ft.ScrollMode.AUTO
            ),
        ], spacing=10),
        padding=15
    )

    # Create a card for configuration
    config_title = ft.Text("Configurações de Salvamento", size=16, weight=ft.FontWeight.BOLD)

    config_card = create_card(
        content=ft.Column([
            config_title,
            ft.Divider(),
            ft.Row([
                automated_save_dir,
                ft.IconButton(
                    icon=ft.icons.FOLDER_OPEN,
                    tooltip="Selecionar diretório",
                    on_click=select_automated_save_dir
                ),
            ]),
            ft.Container(
                content=ft.Column([
                    automated_file_format_title,
                    automated_file_format,
                ], spacing=5),
            ),
        ], spacing=15),
        padding=15
    )

    # Create a card for controls
    controls_title = ft.Text("Controles de Execução", size=16, weight=ft.FontWeight.BOLD)

    controls_card = create_card(
        content=ft.Column([
            controls_title,
            ft.Divider(),
            ft.Row([
                start_button,
                pause_button,
                stop_button,
                automated_progress,
            ], alignment=ft.MainAxisAlignment.START),
            automated_status,
        ], spacing=15),
        padding=15
    )

    # Layout for automated search tab
    automated_search_tab = ft.Container(
        content=ft.Column([
            # Title and description
            ft.Container(
                content=ft.Column([
                    ft.Text("Busca Automatizada", size=20, weight=ft.FontWeight.BOLD, color=PRIMARY_COLOR),
                    ft.Text("Configure até 17 consultas para execução automática em sequência.", color=TEXT_SECONDARY),
                ], spacing=5),
                margin=ft.margin.only(bottom=20)
            ),

            # Search queries card
            search_queries_card,

            # Configuration and controls in a row
            ft.ResponsiveRow([
                # Left column - Configuration
                ft.Column([
                    config_card,
                ], col={"sm": 12, "md": 6}, spacing=20),

                # Right column - Controls
                ft.Column([
                    controls_card,
                ], col={"sm": 12, "md": 6}, spacing=20),
            ]),
        ], spacing=20),
        padding=20
    )

    # ===== MAIN TABS CONTAINER =====

    # Create the tabs
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="Mensagens",
                icon=ft.icons.CHAT,
                content=messaging_tab,
            ),
            ft.Tab(
                text="Google Maps",
                icon=ft.icons.MAP,
                content=google_maps_tab,
            ),
            ft.Tab(
                text="Busca Automatizada",
                icon=ft.icons.SEARCH,
                content=automated_search_tab,
            ),
        ],
    )

    # Add header with logo and app name
    try:
        logo = ft.Image(
            src="assets/img/logo.png",
            width=80,
            height=80,
            fit=ft.ImageFit.CONTAIN,
        )
    except Exception:
        logo = ft.Text(
            "P",
            size=40,
            weight=ft.FontWeight.BOLD,
            color=PRIMARY_COLOR,
            text_align=ft.TextAlign.CENTER,
        )

    # Create app header
    header_text = ft.Text(
        "PROSPECTO",
        size=28,
        weight=ft.FontWeight.BOLD,
        color=PRIMARY_COLOR,
    )

    # Create header with logo and app name
    header = ft.Container(
        content=ft.Row(
            [
                logo,
                ft.Container(width=20),  # Spacer
                ft.Column(
                    [
                        header_text,
                        ft.Text(
                            "Sistema de Geração de Leads",
                            size=16,
                            color=TEXT_SECONDARY,
                        ),
                    ],
                    spacing=5,
                ),
            ],
            alignment=ft.MainAxisAlignment.START,
            vertical_alignment=ft.CrossAxisAlignment.CENTER,
        ),
        padding=ft.padding.only(left=PADDING_LARGE, top=PADDING_LARGE, right=PADDING_LARGE, bottom=PADDING_MEDIUM),
        bgcolor=CARD_COLOR,
        shadow=[SHADOW_MEDIUM],
    )

    # Create footer
    footer = ft.Container(
        content=ft.Row(
            [
                ft.Text(
                    "© 2025 PROSPECTO - Todos os direitos reservados",
                    size=12,
                    color=TEXT_SECONDARY,
                ),
                ft.Container(
                    content=ft.Row(
                        [
                            ft.IconButton(
                                icon=ft.icons.HELP_OUTLINE,
                                icon_color=TEXT_SECONDARY,
                                tooltip="Ajuda",
                                icon_size=20,
                            ),
                            ft.IconButton(
                                icon=ft.icons.SETTINGS_OUTLINED,
                                icon_color=TEXT_SECONDARY,
                                tooltip="Configurações",
                                icon_size=20,
                            ),
                        ]
                    ),
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        ),
        padding=PADDING_MEDIUM,
        bgcolor=CARD_COLOR,
        shadow=[SHADOW_MEDIUM],
    )

    # Create main content area
    content_area = ft.Container(
        content=tabs,
        expand=True,
        margin=ft.margin.only(top=PADDING_MEDIUM, bottom=PADDING_MEDIUM),
        padding=PADDING_MEDIUM,
    )

    # Add everything to the page with a modern layout
    page.add(
        ft.Column(
            [
                header,
                content_area,
                footer,
            ],
            spacing=0,
            expand=True,
        )
    )
