"""
Janela de diálogo de configurações para o aplicativo PROSPECTO.
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QComboBox, QCheckBox, QLineEdit, QRadioButton, QGroupBox,
                            QFileDialog, QMessageBox, QButtonGroup)
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QIcon, QDesktopServices

import config_manager
from desktop_theme import (
    style_heading_label, style_body_label, TEXT_SECONDARY,
    style_primary_button, style_secondary_button
)

class SettingsDialog(QDialog):
    """Janela de diálogo para configurações do aplicativo."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurações do PROSPECTO")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        self.setWindowIcon(QIcon.fromTheme("preferences-system"))
        self.setModal(True)

        # Carregar configurações atuais
        self.config = config_manager.load_config()

        # Inicializar a interface
        self.init_ui()

    def init_ui(self):
        """Inicializa a interface da janela de diálogo."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Título
        title = QLabel("Configurações do Sistema")
        style_heading_label(title)
        layout.addWidget(title)

        # Seção: Configurações de IA
        ai_group = QGroupBox("Configurações de IA")
        ai_layout = QVBoxLayout(ai_group)

        # Provedor de IA
        provider_layout = QHBoxLayout()
        provider_label = QLabel("Provedor de IA:")
        style_body_label(provider_label)

        self.ai_provider_combo = QComboBox()
        self.ai_provider_combo.addItems(["OpenAI", "Anthropic", "Google", "Outro"])
        self.ai_provider_combo.setCurrentText(self.config.get("ai_provider", "OpenAI"))

        provider_layout.addWidget(provider_label)
        provider_layout.addWidget(self.ai_provider_combo)
        ai_layout.addLayout(provider_layout)

        # Modelo de IA
        model_layout = QHBoxLayout()
        model_label = QLabel("Modelo de IA:")
        style_body_label(model_label)

        self.ai_model_combo = QComboBox()
        self.ai_model_combo.addItems(["GPT-4 Mini", "GPT-3.5", "GPT-4", "Claude 3 Sonnet", "Gemini Pro"])
        self.ai_model_combo.setCurrentText(self.config.get("ai_model", "GPT-4 Mini"))

        model_layout.addWidget(model_label)
        model_layout.addWidget(self.ai_model_combo)
        ai_layout.addLayout(model_layout)

        # Chave da API
        api_key_layout = QHBoxLayout()
        api_key_label = QLabel("Chave da API:")
        style_body_label(api_key_label)

        self.api_key_input = QLineEdit(self.config.get("ai_api_key", ""))
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)  # Ocultar a chave como senha
        self.api_key_input.setPlaceholderText("Digite sua chave de API aqui")

        show_key_button = QPushButton("👁️")
        show_key_button.setToolTip("Mostrar/Ocultar Chave")
        show_key_button.setFixedWidth(30)
        show_key_button.clicked.connect(self.toggle_api_key_visibility)

        api_key_layout.addWidget(api_key_label)
        api_key_layout.addWidget(self.api_key_input)
        api_key_layout.addWidget(show_key_button)
        ai_layout.addLayout(api_key_layout)

        # Descrição da API
        api_description = QLabel(
            "A chave da API é necessária para utilizar os recursos de IA para sugestões de busca. "
            "Você pode obter uma chave gratuita no site do provedor selecionado."
        )
        api_description.setWordWrap(True)
        api_description.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 12px;")
        ai_layout.addWidget(api_description)

        # Botão para obter chave
        get_key_button = QPushButton("Como obter uma chave de API")
        get_key_button.setIcon(QIcon.fromTheme("help-contents"))
        style_secondary_button(get_key_button)
        get_key_button.clicked.connect(self.show_api_key_help)
        ai_layout.addWidget(get_key_button)

        layout.addWidget(ai_group)

        # Seção: Modo de Execução do Chrome
        chrome_group = QGroupBox("Modo de Execução do Chrome")
        chrome_layout = QVBoxLayout(chrome_group)

        chrome_description = QLabel("Configure como o Chrome será executado durante as buscas:")
        style_body_label(chrome_description)
        chrome_layout.addWidget(chrome_description)

        self.headless_checkbox = QCheckBox("Executar em segundo plano (headless)")
        self.headless_checkbox.setChecked(self.config.get("headless_mode", True))
        self.headless_checkbox.setToolTip("Quando ativado, o navegador funciona em segundo plano sem exibir janela")
        chrome_layout.addWidget(self.headless_checkbox)

        layout.addWidget(chrome_group)

        # Seção: Diretório Padrão
        dir_group = QGroupBox("Diretório Padrão para Salvar Arquivos")
        dir_layout = QVBoxLayout(dir_group)

        dir_description = QLabel("Defina o diretório padrão para salvar os arquivos exportados:")
        style_body_label(dir_description)
        dir_layout.addWidget(dir_description)

        dir_input_layout = QHBoxLayout()
        self.dir_input = QLineEdit(self.config.get("default_save_dir", ""))
        self.dir_input.setReadOnly(True)

        browse_button = QPushButton("Selecionar")
        browse_button.setIcon(QIcon.fromTheme("folder-open"))
        style_secondary_button(browse_button)
        browse_button.clicked.connect(self.select_directory)

        dir_input_layout.addWidget(self.dir_input)
        dir_input_layout.addWidget(browse_button)
        dir_layout.addLayout(dir_input_layout)

        layout.addWidget(dir_group)

        # Seção: Formato Padrão de Exportação
        format_group = QGroupBox("Formato Padrão de Exportação")
        format_layout = QVBoxLayout(format_group)

        format_description = QLabel("Selecione o formato padrão para exportação de dados:")
        style_body_label(format_description)
        format_layout.addWidget(format_description)

        self.format_group = QButtonGroup(self)
        self.excel_radio = QRadioButton("Excel (.xlsx)")
        self.csv_radio = QRadioButton("CSV (.csv)")

        if self.config.get("default_export_format", "excel") == "excel":
            self.excel_radio.setChecked(True)
        else:
            self.csv_radio.setChecked(True)

        self.format_group.addButton(self.excel_radio)
        self.format_group.addButton(self.csv_radio)

        format_layout.addWidget(self.excel_radio)
        format_layout.addWidget(self.csv_radio)

        layout.addWidget(format_group)

        # Botões de ação
        buttons_layout = QHBoxLayout()

        self.restore_button = QPushButton("Restaurar Padrões")
        self.restore_button.setIcon(QIcon.fromTheme("edit-undo"))
        style_secondary_button(self.restore_button)
        self.restore_button.clicked.connect(self.restore_defaults)

        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.setIcon(QIcon.fromTheme("dialog-cancel"))
        style_secondary_button(self.cancel_button)
        self.cancel_button.clicked.connect(self.reject)

        self.save_button = QPushButton("Salvar")
        self.save_button.setIcon(QIcon.fromTheme("document-save"))
        style_primary_button(self.save_button)
        self.save_button.clicked.connect(self.save_settings)

        buttons_layout.addWidget(self.restore_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)

        layout.addLayout(buttons_layout)

    def select_directory(self):
        """Abre um diálogo para selecionar o diretório padrão."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório Padrão",
            self.dir_input.text()
        )
        if directory:
            self.dir_input.setText(directory)

    def restore_defaults(self):
        """Restaura as configurações para os valores padrão."""
        reply = QMessageBox.question(
            self,
            "Restaurar Padrões",
            "Tem certeza que deseja restaurar todas as configurações para os valores padrão?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Restaurar valores nos controles
            self.ai_provider_combo.setCurrentText(config_manager.DEFAULT_CONFIG["ai_provider"])
            self.ai_model_combo.setCurrentText(config_manager.DEFAULT_CONFIG["ai_model"])
            self.api_key_input.setText(config_manager.DEFAULT_CONFIG["ai_api_key"])
            self.headless_checkbox.setChecked(config_manager.DEFAULT_CONFIG["headless_mode"])
            self.dir_input.setText(config_manager.DEFAULT_CONFIG["default_save_dir"])

            if config_manager.DEFAULT_CONFIG["default_export_format"] == "excel":
                self.excel_radio.setChecked(True)
            else:
                self.csv_radio.setChecked(True)

    def toggle_api_key_visibility(self):
        """Alterna a visibilidade da chave da API."""
        if self.api_key_input.echoMode() == QLineEdit.EchoMode.Password:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)

    def show_api_key_help(self):
        """Exibe informações sobre como obter uma chave de API."""
        provider = self.ai_provider_combo.currentText()

        help_text = "Como obter uma chave de API:\n\n"

        if provider == "OpenAI":
            help_text += (
                "1. Acesse https://platform.openai.com/signup\n"
                "2. Crie uma conta ou faça login\n"
                "3. Vá para 'API Keys' no menu\n"
                "4. Clique em 'Create new secret key'\n"
                "5. Copie a chave gerada e cole no campo 'Chave da API'\n\n"
                "Deseja visitar o site da OpenAI agora?"
            )
            url = "https://platform.openai.com/signup"
        elif provider == "Anthropic":
            help_text += (
                "1. Acesse https://console.anthropic.com/\n"
                "2. Crie uma conta ou faça login\n"
                "3. Vá para 'API Keys' no menu\n"
                "4. Clique em 'Create Key'\n"
                "5. Copie a chave gerada e cole no campo 'Chave da API'\n\n"
                "Deseja visitar o site da Anthropic agora?"
            )
            url = "https://console.anthropic.com/"
        elif provider == "Google":
            help_text += (
                "1. Acesse https://makersuite.google.com/app/apikey\n"
                "2. Crie uma conta ou faça login\n"
                "3. Clique em 'Create API Key'\n"
                "4. Copie a chave gerada e cole no campo 'Chave da API'\n\n"
                "Deseja visitar o site do Google AI Studio agora?"
            )
            url = "https://makersuite.google.com/app/apikey"
        else:
            help_text += (
                "Consulte a documentação do provedor selecionado para obter informações "
                "sobre como gerar uma chave de API."
            )
            url = ""

        # Perguntar se deseja abrir o site
        if url:
            reply = QMessageBox.question(
                self,
                "Obter Chave de API",
                help_text,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                QDesktopServices.openUrl(QUrl(url))
        else:
            QMessageBox.information(
                self,
                "Obter Chave de API",
                help_text
            )

    def save_settings(self):
        """Salva as configurações."""
        # Atualizar o dicionário de configurações
        self.config["ai_model"] = self.ai_model_combo.currentText()
        self.config["ai_provider"] = self.ai_provider_combo.currentText()
        self.config["ai_api_key"] = self.api_key_input.text()
        self.config["headless_mode"] = self.headless_checkbox.isChecked()
        self.config["default_save_dir"] = self.dir_input.text()
        self.config["default_export_format"] = "excel" if self.excel_radio.isChecked() else "csv"

        # Salvar no arquivo
        if config_manager.save_config(self.config):
            QMessageBox.information(
                self,
                "Sucesso",
                "Configurações salvas com sucesso!"
            )
            self.accept()
        else:
            QMessageBox.warning(
                self,
                "Erro",
                "Ocorreu um erro ao salvar as configurações."
            )
