"""
Janela de diálogo de ajuda para o aplicativo PROSPECTO.
"""
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTabWidget, QWidget, QTextBrowser)
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QIcon, QPixmap, QDesktopServices

import config_manager
from desktop_theme import (
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR,
    style_heading_label, style_subheading_label, style_body_label,
    create_card_frame, style_primary_button, style_secondary_button,
    get_logo_pixmap
)

class HelpDialog(QDialog):
    """Janela de diálogo de ajuda do aplicativo."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Ajuda do PROSPECTO")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)
        self.setWindowIcon(QIcon.fromTheme("help-about"))
        self.setModal(True)
        
        # Carregar configurações
        self.config = config_manager.load_config()
        
        # Inicializar a interface
        self.init_ui()
        
    def init_ui(self):
        """Inicializa a interface da janela de diálogo."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Cabeçalho com logo e título
        header_layout = QHBoxLayout()
        
        # Logo
        logo_pixmap = get_logo_pixmap()
        if logo_pixmap:
            logo_label = QLabel()
            logo_label.setPixmap(logo_pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio))
            header_layout.addWidget(logo_label)
        
        # Título e versão
        title_layout = QVBoxLayout()
        title = QLabel("PROSPECTO")
        style_heading_label(title)
        
        version = QLabel(f"Versão {self.config.get('app_version', '1.0.0')}")
        version.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 14px;")
        
        title_layout.addWidget(title)
        title_layout.addWidget(version)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Abas de ajuda
        tabs = QTabWidget()
        
        # Aba Sobre
        about_tab = QWidget()
        about_layout = QVBoxLayout(about_tab)
        
        about_text = QTextBrowser()
        about_text.setOpenExternalLinks(True)
        about_text.setHtml(f"""
        <h2 style="color: {PRIMARY_COLOR};">Sobre o PROSPECTO</h2>
        <p>O PROSPECTO é um sistema avançado de geração de leads desenvolvido para profissionais 
        que desejam expandir sua base de clientes de forma eficiente e automatizada.</p>
        
        <p>Com o PROSPECTO, você pode:</p>
        <ul>
            <li>Buscar leads no Google Maps com base em critérios específicos</li>
            <li>Automatizar buscas para coletar até 1000 leads em diferentes regiões</li>
            <li>Enviar mensagens personalizadas via WhatsApp</li>
            <li>Gerenciar e organizar seus contatos de forma eficiente</li>
        </ul>
        
        <h3 style="color: {PRIMARY_COLOR};">Empresa Desenvolvedora</h3>
        <p><strong>Asimov Tech Solutions</strong><br>
        Especialistas em soluções tecnológicas para automação e geração de leads.</p>
        <p>Site: <a href="https://asimovtech.systems/">https://asimovtech.systems/</a></p>
        """)
        
        about_layout.addWidget(about_text)
        tabs.addTab(about_tab, "Sobre")
        
        # Aba Suporte
        support_tab = QWidget()
        support_layout = QVBoxLayout(support_tab)
        
        support_text = QTextBrowser()
        support_text.setOpenExternalLinks(True)
        support_text.setHtml(f"""
        <h2 style="color: {PRIMARY_COLOR};">Suporte Técnico</h2>
        <p>Se você precisar de ajuda com o PROSPECTO, entre em contato com nossa equipe de suporte:</p>
        
        <h3 style="color: {PRIMARY_COLOR};">Contato para Suporte</h3>
        <p><strong>WhatsApp:</strong> (21) 98230-1476</p>
        <p><strong>E-mail:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
        
        <h3 style="color: {PRIMARY_COLOR};">Horário de Atendimento</h3>
        <p>Segunda a Sexta: 9h às 18h<br>
        Sábado: 9h às 13h</p>
        
        <p>Para suporte emergencial fora do horário comercial, envie uma mensagem via WhatsApp 
        com o assunto "URGENTE" e retornaremos o mais breve possível.</p>
        """)
        
        support_layout.addWidget(support_text)
        tabs.addTab(support_tab, "Suporte")
        
        # Aba Links Úteis
        links_tab = QWidget()
        links_layout = QVBoxLayout(links_tab)
        
        links_text = QTextBrowser()
        links_text.setOpenExternalLinks(True)
        links_text.setHtml(f"""
        <h2 style="color: {PRIMARY_COLOR};">Links Úteis</h2>
        
        <h3 style="color: {PRIMARY_COLOR};">Documentação</h3>
        <p><a href="https://asimovtech.systems/docs/prospecto/">Manual do Usuário</a> - Guia completo de uso do PROSPECTO</p>
        <p><a href="https://asimovtech.systems/docs/prospecto/faq/">Perguntas Frequentes</a> - Respostas para as dúvidas mais comuns</p>
        
        <h3 style="color: {PRIMARY_COLOR};">Tutoriais</h3>
        <p><a href="https://asimovtech.systems/tutoriais/prospecto/busca-automatizada/">Como usar a Busca Automatizada</a></p>
        <p><a href="https://asimovtech.systems/tutoriais/prospecto/mensagens-personalizadas/">Criando Mensagens Personalizadas Eficientes</a></p>
        <p><a href="https://asimovtech.systems/tutoriais/prospecto/exportacao-dados/">Exportando e Gerenciando seus Leads</a></p>
        
        <h3 style="color: {PRIMARY_COLOR};">Recursos Adicionais</h3>
        <p><a href="https://asimovtech.systems/blog/estrategias-captacao-leads/">Estratégias para Captação de Leads</a></p>
        <p><a href="https://asimovtech.systems/blog/marketing-digital-pequenas-empresas/">Marketing Digital para Pequenas Empresas</a></p>
        """)
        
        links_layout.addWidget(links_text)
        tabs.addTab(links_tab, "Links Úteis")
        
        layout.addWidget(tabs)
        
        # Botões de ação
        buttons_layout = QHBoxLayout()
        
        website_button = QPushButton("Visitar Site da Empresa")
        website_button.setIcon(QIcon.fromTheme("web-browser"))
        style_secondary_button(website_button)
        website_button.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("https://asimovtech.systems/")))
        
        close_button = QPushButton("Fechar")
        close_button.setIcon(QIcon.fromTheme("window-close"))
        style_primary_button(close_button)
        close_button.clicked.connect(self.accept)
        
        buttons_layout.addWidget(website_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
