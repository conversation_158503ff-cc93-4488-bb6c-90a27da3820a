"""
Database operations for message management.
"""
import sqlite3
import uuid
from datetime import datetime
import csv
import os
from pathlib import Path

import config
from utils.filtro_similaridade import calculate_hash, is_similar_to_any

def get_connection():
    """Get a connection to the SQLite database."""
    return sqlite3.connect(config.DB_PATH)

def save_message(message, version="v1"):
    """
    Save a new message to the database if it doesn't exist.
    
    Args:
        message (str): The message text
        version (str, optional): Message version. Defaults to "v1".
        
    Returns:
        bool: True if saved successfully, False if already exists
    """
    conn = get_connection()
    cursor = conn.cursor()
    
    message_hash = calculate_hash(message)
    
    # Check if hash already exists
    cursor.execute("SELECT id FROM messages WHERE message_hash = ?", (message_hash,))
    if cursor.fetchone():
        conn.close()
        return False
    
    # Check similarity with existing messages
    cursor.execute("SELECT message FROM messages")
    existing_messages = [row[0] for row in cursor.fetchall()]
    
    if is_similar_to_any(message, existing_messages):
        conn.close()
        return False
    
    # Save the new message
    now = datetime.now().isoformat()
    message_uuid = str(uuid.uuid4())
    
    cursor.execute(
        "INSERT INTO messages (message, message_hash, created_at, used, version, uuid) VALUES (?, ?, ?, ?, ?, ?)",
        (message, message_hash, now, False, version, message_uuid)
    )
    
    conn.commit()
    conn.close()
    return True

def get_unused_messages(limit=10):
    """
    Get a list of unused messages.
    
    Args:
        limit (int, optional): Maximum number of messages to retrieve. Defaults to 10.
        
    Returns:
        list: List of message dictionaries
    """
    conn = get_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute(
        "SELECT id, message, created_at, version, uuid FROM messages WHERE used = 0 ORDER BY created_at LIMIT ?",
        (limit,)
    )
    
    messages = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return messages

def mark_message_as_used(message_id, conversion=False):
    """
    Mark a message as used.
    
    Args:
        message_id (int): The message ID
        conversion (bool, optional): Whether the message resulted in a conversion. Defaults to False.
        
    Returns:
        bool: True if updated successfully, False otherwise
    """
    conn = get_connection()
    cursor = conn.cursor()
    
    now = datetime.now().isoformat()
    
    cursor.execute(
        "UPDATE messages SET used = 1, last_sent = ?, conversion = ? WHERE id = ?",
        (now, conversion, message_id)
    )
    
    success = cursor.rowcount > 0
    conn.commit()
    conn.close()
    
    return success

def export_used_messages_to_csv():
    """
    Export used messages to a CSV file.
    
    Returns:
        str: Path to the exported CSV file
    """
    conn = get_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    now = datetime.now()
    year_month = f"{now.year}_{now.month:02d}"
    
    file_name = config.USADAS_FILE_TEMPLATE.format(year=now.year, month=f"{now.month:02d}")
    file_path = config.MENSAGENS_DIR / file_name
    
    cursor.execute(
        "SELECT id, message, created_at, last_sent, version, conversion, uuid FROM messages WHERE used = 1"
    )
    
    rows = cursor.fetchall()
    
    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'message', 'created_at', 'last_sent', 'version', 'conversion', 'uuid']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in rows:
            writer.writerow(dict(row))
    
    conn.close()
    
    return str(file_path)

def export_candidate_messages_to_csv():
    """
    Export unused (candidate) messages to a CSV file.
    
    Returns:
        str: Path to the exported CSV file
    """
    conn = get_connection()
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    file_path = config.CANDIDATAS_FILE
    
    cursor.execute(
        "SELECT id, message, created_at, version, uuid FROM messages WHERE used = 0"
    )
    
    rows = cursor.fetchall()
    
    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'message', 'created_at', 'version', 'uuid']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in rows:
            writer.writerow(dict(row))
    
    conn.close()
    
    return str(file_path)
