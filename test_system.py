"""
Test script for the message generation and management system.
"""
import os
import sqlite3
import unittest
from unittest.mock import patch, MagicMock

import config
from utils.filtro_similaridade import similaridade, calculate_hash, pode_enviar
from utils.db_operations import save_message, get_unused_messages, mark_message_as_used

class TestSimilarityFunctions(unittest.TestCase):
    """Test the similarity detection functions."""
    
    def test_calculate_hash(self):
        """Test the hash calculation function."""
        text = "Hello, world!"
        hash1 = calculate_hash(text)
        hash2 = calculate_hash(text)
        hash3 = calculate_hash("Different text")
        
        self.assertEqual(hash1, hash2)
        self.assertNotEqual(hash1, hash3)
    
    def test_similaridade(self):
        """Test the similarity ratio function."""
        text1 = "Hello, how are you today?"
        text2 = "Hello, how are you doing today?"
        text3 = "Completely different message"
        
        sim1_2 = similaridade(text1, text2)
        sim1_3 = similaridade(text1, text3)
        
        self.assertGreater(sim1_2, 0.8)  # Should be very similar
        self.assertLess(sim1_3, 0.5)     # Should be less similar
    
    def test_pode_enviar(self):
        """Test the message sending eligibility function."""
        message = "Hello, this is a test message"
        similar_message = "Hello, this is a test message with slight differences"
        different_message = "Completely different content"
        
        # Test with empty list of sent messages
        self.assertTrue(pode_enviar(message, []))
        
        # Test with non-similar messages
        self.assertTrue(pode_enviar(message, [different_message]))
        
        # Test with similar messages
        self.assertFalse(pode_enviar(message, [similar_message], 0.7))
        
        # Test with custom threshold
        self.assertTrue(pode_enviar(message, [similar_message], 0.99))

class TestDatabaseOperations(unittest.TestCase):
    """Test the database operations."""
    
    def setUp(self):
        """Set up the test database."""
        # Use an in-memory database for testing
        config.DB_PATH = ":memory:"
        
        # Create the database schema
        self.conn = sqlite3.connect(config.DB_PATH)
        self.cursor = self.conn.cursor()
        
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message TEXT NOT NULL,
            message_hash TEXT NOT NULL UNIQUE,
            created_at TEXT NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            last_sent TEXT,
            version TEXT DEFAULT 'v1',
            conversion BOOLEAN DEFAULT FALSE,
            uuid TEXT UNIQUE
        )
        ''')
        
        self.conn.commit()
    
    def tearDown(self):
        """Clean up after the tests."""
        self.conn.close()
    
    def test_save_message(self):
        """Test saving a message to the database."""
        message = "This is a test message"
        
        # Save the message
        result = save_message(message)
        self.assertTrue(result)
        
        # Try to save the same message again
        result = save_message(message)
        self.assertFalse(result)
        
        # Check that the message was saved
        self.cursor.execute("SELECT message FROM messages WHERE message = ?", (message,))
        saved_message = self.cursor.fetchone()
        self.assertIsNotNone(saved_message)
        self.assertEqual(saved_message[0], message)
    
    def test_get_unused_messages(self):
        """Test getting unused messages from the database."""
        # Save some messages
        save_message("Message 1")
        save_message("Message 2")
        save_message("Message 3")
        
        # Mark one as used
        self.cursor.execute("UPDATE messages SET used = 1 WHERE message = ?", ("Message 2",))
        self.conn.commit()
        
        # Get unused messages
        unused = get_unused_messages()
        
        # Should have 2 unused messages
        self.assertEqual(len(unused), 2)
        
        # Check that the used message is not in the results
        messages = [m["message"] for m in unused]
        self.assertIn("Message 1", messages)
        self.assertIn("Message 3", messages)
        self.assertNotIn("Message 2", messages)
    
    def test_mark_message_as_used(self):
        """Test marking a message as used."""
        # Save a message
        save_message("Test message")
        
        # Get the message ID
        self.cursor.execute("SELECT id FROM messages WHERE message = ?", ("Test message",))
        message_id = self.cursor.fetchone()[0]
        
        # Mark as used
        result = mark_message_as_used(message_id)
        self.assertTrue(result)
        
        # Check that it was marked as used
        self.cursor.execute("SELECT used FROM messages WHERE id = ?", (message_id,))
        used = self.cursor.fetchone()[0]
        self.assertEqual(used, 1)
        
        # Check that last_sent was updated
        self.cursor.execute("SELECT last_sent FROM messages WHERE id = ?", (message_id,))
        last_sent = self.cursor.fetchone()[0]
        self.assertIsNotNone(last_sent)

if __name__ == "__main__":
    unittest.main()
