"""
OpenAI API integration for message generation.
"""
import argparse
from openai import OpenAI

import config
from utils.db_operations import save_message

def generate_message_variation(example_message, temperature=None):
    """
    Generate a variation of the example message using OpenAI API.

    Args:
        example_message (str): The example message to generate a variation of
        temperature (float, optional): Temperature parameter for generation.
                                      Defaults to config.DEFAULT_TEMPERATURE.

    Returns:
        str: The generated message variation
    """
    if temperature is None:
        temperature = config.DEFAULT_TEMPERATURE

    # Create client directly with the API key
    client = OpenAI(
        api_key="********************************************************************************************************************************************************************"
    )

    prompt = f"Gere uma nova mensagem similar com mesmo propósito, tom educado, profissional e sem soar como cópia. Exemplo: '{example_message}'"

    response = client.chat.completions.create(
        model=config.OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "Você é um assistente especializado em criar variações de mensagens profissionais."},
            {"role": "user", "content": prompt}
        ],
        temperature=temperature,
        max_tokens=config.DEFAULT_MAX_TOKENS
    )

    return response.choices[0].message.content.strip()

def generate_multiple_variations(example_message, count=3, temperature=None):
    """
    Generate multiple variations of the example message.

    Args:
        example_message (str): The example message to generate variations of
        count (int, optional): Number of variations to generate. Defaults to 3.
        temperature (float, optional): Temperature parameter for generation.
                                      Defaults to config.DEFAULT_TEMPERATURE.

    Returns:
        list: List of generated message variations
    """
    if temperature is None:
        temperature = config.DEFAULT_TEMPERATURE

    # Create client directly with the API key
    client = OpenAI(
        api_key="********************************************************************************************************************************************************************"
    )

    prompt = f"Gere {count} novas mensagens similares com mesmo propósito, tom educado, profissional e sem soar como cópia. Cada mensagem deve ser diferente na estrutura. Exemplo: '{example_message}'"

    response = client.chat.completions.create(
        model=config.OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "Você é um assistente especializado em criar variações de mensagens profissionais."},
            {"role": "user", "content": prompt}
        ],
        temperature=temperature,
        max_tokens=config.DEFAULT_MAX_TOKENS * count
    )

    content = response.choices[0].message.content.strip()

    # Parse the response to extract individual messages
    # This is a simple approach; you might need to adjust based on actual API response format
    messages = []
    for line in content.split('\n'):
        line = line.strip()
        if line and not line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
            messages.append(line)

    # If parsing failed, just return the whole content as one message
    if not messages:
        messages = [content]

    return messages

def save_generated_messages(messages, version="v1"):
    """
    Save generated messages to the database.

    Args:
        messages (list): List of message strings
        version (str, optional): Message version. Defaults to "v1".

    Returns:
        tuple: (number of saved messages, total messages)
    """
    saved_count = 0
    total_count = len(messages)

    for message in messages:
        if save_message(message, version):
            saved_count += 1

    return saved_count, total_count

def main():
    """Main function to generate and save message variations."""
    parser = argparse.ArgumentParser(description="Generate message variations using OpenAI API")
    parser.add_argument("--example", "-e", required=True, help="Example message to generate variations of")
    parser.add_argument("--count", "-c", type=int, default=3, help="Number of variations to generate")
    parser.add_argument("--temperature", "-t", type=float, default=config.DEFAULT_TEMPERATURE,
                        help="Temperature parameter for generation")
    parser.add_argument("--version", "-v", default="v1", help="Version tag for the generated messages")

    args = parser.parse_args()

    try:
        print(f"Generating {args.count} message variations...")
        variations = generate_multiple_variations(args.example, args.count, args.temperature)

        print(f"Generated {len(variations)} variations.")
        for i, variation in enumerate(variations, 1):
            print(f"\n--- Variation {i} ---")
            print(variation)

        saved_count, total_count = save_generated_messages(variations, args.version)
        print(f"\nSaved {saved_count} out of {total_count} variations to the database.")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
