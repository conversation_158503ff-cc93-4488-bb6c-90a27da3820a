"""
Configuration settings for the message generation and management system.
"""
import os
from pathlib import Path

# API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_MODEL = "gpt-4o-mini"

# Database Configuration
DB_PATH = "db.sqlite3"

# Message Generation Configuration
DEFAULT_TEMPERATURE = 1.0  # Higher temperature (1.0-1.3) for more variation
DEFAULT_MAX_TOKENS = 150  # Maximum tokens for generated messages
SIMILARITY_THRESHOLD = 0.95  # Threshold for similarity detection

# File Paths
BASE_DIR = Path(__file__).resolve().parent
MENSAGENS_DIR = BASE_DIR / "mensagens"
USADAS_FILE_TEMPLATE = "usadas_{year}_{month}.csv"
CANDIDATAS_FILE = MENSAGENS_DIR / "candidatas.csv"
BLACKLIST_FILE = MENSAGENS_DIR / "blacklist.txt"

# Ensure directories exist
MENSAGENS_DIR.mkdir(exist_ok=True)
