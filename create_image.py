from PIL import Image, ImageDraw, ImageFont
import os

# Criar uma nova imagem com fundo branco
width = 800
height = 600
background_color = (255, 255, 255)  # Branco
image = Image.new('RGB', (width, height), background_color)

# Preparar para desenhar na imagem
draw = ImageDraw.Draw(image)

# Definir a fonte e tamanho
font_size = 60
try:
    font = ImageFont.truetype("arial.ttf", font_size)
except:
    # Fallback para a fonte padrão se arial não estiver disponível
    font = ImageFont.load_default()

# Texto a ser escrito
text = "PROSPECTO"

# Calcular a posição do texto para centralizá-lo
bbox = draw.textbbox((0, 0), text, font=font)
text_width = bbox[2] - bbox[0]
text_height = bbox[3] - bbox[1]
x = (width - text_width) / 2
y = (height - text_height) / 2

# Desenhar o texto em preto
text_color = (0, 0, 0)  # Preto
draw.text((x, y), text, fill=text_color, font=font)

# Salvar a imagem
output_path = "scrap_google_maps/images/2916315.png"
image.save(output_path)
print(f"Imagem criada com sucesso em: {output_path}")