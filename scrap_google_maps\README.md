# Business-Client-Capture

O **Business-Client-Capture** é uma ferramenta de automação em Python que reúne duas funcionalidades principais:

1. **Consulta no Google Maps:** Uma ferramenta que, a partir de um tipo de negócio e uma localização informada, consulta o Google Maps e extrai diversas informações (até cerca de 120 dados) de cada estabelecimento, como nome, endereço, telefone, website, entre outros.
2. **Captura de Clientes (Envio via WhatsApp):** Uma ferramenta que utiliza os dados extraídos (armazenados em arquivos Excel ou CSV) para enviar mensagens e mídias automaticamente via WhatsApp para os contatos dos estabelecimentos.

---

## Funcionalidades

### 1. Consulta no Google Maps

- **Como Funciona:**
  - Utiliza o Selenium para abrir o navegador Chrome e acessar o Google Maps.
  - Inicialmente, abre o Google Maps na localização geral indicada para melhorar a precisão dos resultados.
  - Em seguida, realiza uma busca combinada com o tipo de negócio e a localização.
  - Percorre os resultados exibidos, clicando em cada estabelecimento para capturar detalhes como nome, endereço, website e número de telefone.
  - Armazena os dados coletados em um arquivo Excel e/ou CSV, que fica disponível no diretório de resultados escolhido pelo usuário.

- **Uso:**
  - É possível definir o número de resultados (estabelecimentos) que serão consultados.
  - Após a extração dos dados, a ferramenta solicita que o usuário escolha o formato de salvamento (Excel ou CSV) e o diretório para armazenar os arquivos.

### 2. Captura de Clientes (Envio via WhatsApp)

- **Como Funciona:**
  - Utiliza o arquivo gerado pela consulta no Google Maps (Excel ou CSV) para enviar mensagens automaticamente via WhatsApp.
  - Possui um temporizador que permite agendar o envio das mensagens para um horário específico. Durante esse período, a ferramenta solicita que o usuário realize o login no WhatsApp Web (através da leitura do QR Code).
  - As mensagens enviadas podem incluir texto e mídias (imagens ou outros arquivos). A lógica de envio é baseada na abertura da conversa com o número do estabelecimento e no clique para enviar os conteúdos.
  - O conteúdo da mensagem é customizável. Basta editar o arquivo `message_templates.py` para ajustar o texto, incluir placeholders como o nome do negócio e a saudação conforme o horário.

- **Diferenciais da Ferramenta Experimental:**
  - O módulo experimental permite, além do envio padrão, testar o envio em dois idiomas (Português e Inglês), detectar o fuso horário com base na localização do negócio e até enviar múltiplas mídias.
  - Possui opções para configurar parâmetros experimentais, como seleção de idioma e mensagem padrão, facilitando a execução em ambientes bilíngues.

---

## Edição e Personalização das Mensagens

### Arquivo: `message_templates.py`

- **Template de Mensagem:**
  - O template principal é definido através de uma função lambda chamada `txt`, que recebe dois parâmetros: `time` (descritor de tempo) e `business` (nome do estabelecimento).
  - Exemplo de template:
    ```python
    txt = lambda time, business: f"""
    {time}, {business} this is a test message for my automation application.
    Check it out at: https://github.com/Superjoa10/business-client-capture/tree/main
    """
    ```
- **Denominações de Tempo:**
  - A variável `time_var` é uma lista que contém as saudações baseadas no horário (por exemplo, "Good Morning", "Good Evening", "Good Night"). Essa lista pode ser modificada para se adequar a outros idiomas ou preferências.

- **Personalização:**
  - Para incluir informações adicionais, como o endereço do estabelecimento, basta editar o template e adicionar novos placeholders. Por exemplo:
    ```python
    txt = lambda time, business, address: f"""
    {time}, {business} localizado em {address}, esta é uma mensagem de teste da minha aplicação de automação.
    Confira mais detalhes no repositório: https://github.com/Superjoa10/business-client-capture/tree/main
    """
    ```
  - Atenção: Caso altere a assinatura da função lambda, lembre-se de ajustar também a forma como ela é chamada nos demais módulos.

- **Envio de Imagens ou Arquivos:**
  - Para modificar o envio de mídias, edite o arquivo de envio (por exemplo, `whatsapp_sender.py` ou `whatsapp_sender_experimental.py`) na função `send_message`, especificamente na parte onde a função `enviar_midia` é chamada. Você pode adicionar mais instâncias, alterar o caminho do arquivo enviado ou remover completamente o envio de mídia, conforme sua necessidade.

---

## Requisitos e Configuração

- **Ambiente Python:**
  - Python 3.x
  - Selenium
  - Pandas
  - Tkinter (geralmente incluído com o Python)
  - PyAutoGUI
  - Outras dependências podem ser instaladas via `pip` (verifique o `requirements.txt` se disponível).

- **Navegador:**
  - O código utiliza o Chrome. Certifique-se de que o `chromedriver.exe` esteja na mesma pasta do projeto ou no PATH do sistema, e que sua versão seja compatível com a versão do Chrome instalada.

- **WhatsApp Web:**
  - Para o envio automático, é necessário realizar o login no WhatsApp Web por meio do QR Code exibido pelo navegador controlado pelo Selenium.

---

## Modo de Uso

### Consulta no Google Maps
1. Execute o módulo `google_maps_scraper.py` (ou use a interface gráfica se implementada).
2. Informe o nome/termo para a consulta, a localização desejada e o número de resultados.
3. Após a extração dos dados, escolha o formato de salvamento (Excel ou CSV) e o diretório para armazenar os arquivos.

### Captura de Clientes via WhatsApp
1. Execute o módulo `whatsapp_sender.py` ou `whatsapp_sender_experimental.py` (existem interfaces gráficas que facilitam o uso).
2. Selecione a funcionalidade desejada:
   - **Time Loop:** Agenda o envio para um horário específico.
   - **Enviar Agora:** Inicia o envio imediatamente.
   - **Consulta Google Maps:** Executa a consulta e gera o arquivo de dados.
   - **Testes:** Permite testar o envio de mensagem ou a compatibilidade do arquivo.
3. Siga as instruções na tela para selecionar o arquivo de dados, fazer o login no WhatsApp e iniciar o envio.

---

## Observações Finais

- **Personalização:**  
  Toda a personalização (mensagens, imagens, configurações de idioma e fuso horário) pode ser realizada editando os respectivos arquivos: `message_templates.py`, `whatsapp_sender.py` e `whatsapp_sender_experimental.py`.

- **Interface Gráfica:**  
  As versões mais recentes dos módulos utilizam o Tkinter para facilitar a interação com o usuário, eliminando a necessidade de inputs via terminal e melhorando a usabilidade.

- **Desenvolvimento Contínuo:**  
  Este projeto está em constante desenvolvimento. Novas funcionalidades e melhorias podem ser adicionadas, como a integração com outros serviços ou a expansão das opções de personalização.

- **Contribuição:**  
  Sinta-se à vontade para contribuir com sugestões, correções e novas ideias para aprimorar a ferramenta.

Acesse o [repositório oficial](https://github.com/Superjoa10/business-client-capture) para mais informações e atualizações.

---
