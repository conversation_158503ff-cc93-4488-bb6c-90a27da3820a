import os
from openai import OpenAI

# Use the existing API key from config.py if available
try:
    from config import OPENAI_API_KEY, OPENAI_MODEL
except ImportError:
    # Default values if config.py is not available
    OPENAI_API_KEY = "********************************************************************************************************************************************************************"
    OPENAI_MODEL = "gpt-4o-mini"

def get_search_suggestions(partial_query, num_suggestions=5):
    """
    Get search suggestions for a partial query using GPT-4 Mini.
    
    Args:
        partial_query (str): Partial search query
        num_suggestions (int): Number of suggestions to return
        
    Returns:
        list: List of search suggestions
    """
    try:
        # Create OpenAI client
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        # Create prompt for GPT
        prompt = f"""
        Você é um assistente especializado em sugerir termos de busca para o Google Maps no Brasil.
        
        Com base no termo parcial "{partial_query}", sugira {num_suggestions} termos de busca completos 
        que seriam úteis para encontrar empresas e profissionais no Google Maps.
        
        Cada sugestão deve seguir o formato "TERMO + LOCALIZAÇÃO" (por exemplo, "DENTISTA + RJ").
        
        Forneça apenas a lista de sugestões, sem explicações adicionais.
        """
        
        # Call GPT API
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um assistente especializado em sugerir termos de busca para o Google Maps no Brasil."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=150
        )
        
        # Extract suggestions from response
        suggestions_text = response.choices[0].message.content.strip()
        
        # Parse suggestions (assuming they're in a list format)
        suggestions = []
        for line in suggestions_text.split('\n'):
            # Clean up the line
            clean_line = line.strip()
            # Remove list markers like "1.", "-", "*", etc.
            if clean_line and (clean_line[0].isdigit() or clean_line[0] in ['-', '*', '•']):
                clean_line = clean_line.split(' ', 1)[1].strip()
            if clean_line:
                suggestions.append(clean_line)
        
        # Limit to requested number
        return suggestions[:num_suggestions]
        
    except Exception as e:
        print(f"Error getting search suggestions: {str(e)}")
        # Return some default suggestions if API call fails
        return [
            "DENTISTA + RJ",
            "ADVOGADO + SP",
            "CONTADOR + MG",
            "MÉDICO + BA",
            "ARQUITETO + PR"
        ]

def get_profession_suggestions(num_suggestions=10):
    """
    Get suggestions for professional categories using GPT-4 Mini.
    
    Args:
        num_suggestions (int): Number of suggestions to return
        
    Returns:
        list: List of profession suggestions
    """
    try:
        # Create OpenAI client
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        # Create prompt for GPT
        prompt = f"""
        Você é um assistente especializado em sugerir categorias de profissionais para busca no Google Maps no Brasil.
        
        Sugira {num_suggestions} categorias de profissionais que seriam úteis para prospecção de clientes.
        
        Forneça apenas a lista de categorias, sem explicações adicionais.
        """
        
        # Call GPT API
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um assistente especializado em sugerir categorias de profissionais para busca no Google Maps no Brasil."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=150
        )
        
        # Extract suggestions from response
        suggestions_text = response.choices[0].message.content.strip()
        
        # Parse suggestions (assuming they're in a list format)
        suggestions = []
        for line in suggestions_text.split('\n'):
            # Clean up the line
            clean_line = line.strip()
            # Remove list markers like "1.", "-", "*", etc.
            if clean_line and (clean_line[0].isdigit() or clean_line[0] in ['-', '*', '•']):
                clean_line = clean_line.split(' ', 1)[1].strip()
            if clean_line:
                suggestions.append(clean_line)
        
        # Limit to requested number
        return suggestions[:num_suggestions]
        
    except Exception as e:
        print(f"Error getting profession suggestions: {str(e)}")
        # Return some default suggestions if API call fails
        return [
            "DENTISTA",
            "ADVOGADO",
            "CONTADOR",
            "MÉDICO",
            "ARQUITETO",
            "ENGENHEIRO",
            "PSICÓLOGO",
            "FISIOTERAPEUTA",
            "NUTRICIONISTA",
            "PERSONAL TRAINER"
        ]

def get_location_suggestions(num_suggestions=10):
    """
    Get suggestions for locations using GPT-4 Mini.
    
    Args:
        num_suggestions (int): Number of suggestions to return
        
    Returns:
        list: List of location suggestions
    """
    try:
        # Create OpenAI client
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        # Create prompt for GPT
        prompt = f"""
        Você é um assistente especializado em sugerir localizações para busca no Google Maps no Brasil.
        
        Sugira {num_suggestions} localizações (estados, cidades ou bairros) que seriam úteis para prospecção de clientes.
        
        Forneça apenas a lista de localizações, sem explicações adicionais.
        """
        
        # Call GPT API
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "Você é um assistente especializado em sugerir localizações para busca no Google Maps no Brasil."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=150
        )
        
        # Extract suggestions from response
        suggestions_text = response.choices[0].message.content.strip()
        
        # Parse suggestions (assuming they're in a list format)
        suggestions = []
        for line in suggestions_text.split('\n'):
            # Clean up the line
            clean_line = line.strip()
            # Remove list markers like "1.", "-", "*", etc.
            if clean_line and (clean_line[0].isdigit() or clean_line[0] in ['-', '*', '•']):
                clean_line = clean_line.split(' ', 1)[1].strip()
            if clean_line:
                suggestions.append(clean_line)
        
        # Limit to requested number
        return suggestions[:num_suggestions]
        
    except Exception as e:
        print(f"Error getting location suggestions: {str(e)}")
        # Return some default suggestions if API call fails
        return [
            "RJ",
            "SP",
            "MG",
            "BA",
            "PR",
            "RS",
            "PE",
            "CE",
            "DF",
            "GO"
        ]
