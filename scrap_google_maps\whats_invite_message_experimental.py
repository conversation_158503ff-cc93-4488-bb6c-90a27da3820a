import time
import urllib
import datetime
import pytz
import threading
import os
import pandas as pd
import pyautogui as gui

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.common.exceptions import (NoSuchElementException, InvalidArgumentException, 
                                        UnexpectedAlertPresentException, TimeoutException)
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from tkinter import Tk, Toplevel, Label, Entry, Button, messagebox, filedialog
from google_maps_scraper import main_query
from configparser import ConfigParser

# ========= Configurações experimentais =========
# Define funções de template para cada idioma
pt_txt = lambda Horario, Nome: (f"""{Horar<PERSON>}, representante da {Nome}, Como vai?  
Esse é meu projetp""")
eng_txt = lambda Horario, Nome: (f"""{<PERSON><PERSON><PERSON>}, Representative of {Nome}. My name is <PERSON>joa10 and this is my project""")

def get_settings():
    # Aqui poderia ser lido um arquivo de configuração (configs.ini)
    global CONTRY_AVARAGE, TIME_AVARAGE, MESSAGE_LANGUAGE
    CONTRY_AVARAGE = 'brazil'
    TIME_AVARAGE = ('Bom dia', 'Good morning')
    MESSAGE_LANGUAGE = 'portugues'

# ========= Funções utilitárias =========

# Limpa o terminal (se necessário)
clear = lambda: os.system('cls' if os.name == 'nt' else 'clear')

def open_file_dialog():
    root = Tk()
    root.withdraw()
    root.update()
    file_path = filedialog.askopenfilename(title="Selecione o arquivo (Excel ou CSV)")
    root.destroy()
    return file_path

def get_navegador():
    # Abre o navegador (Chrome) e aguarda o login no WhatsApp Web
    navegador = webdriver.Chrome()
    navegador.get("https://web.whatsapp.com/")
    while len(navegador.find_elements(By.ID, 'side')) < 1:
        time.sleep(1)
    return navegador

def enviar_midia(navegador, midia):
    navegador.find_element(By.XPATH, '//*[@id="main"]/footer/div[1]/div/span[2]/div/div[1]/div[2]/div/div/div/span').click()
    attach = navegador.find_element(By.CSS_SELECTOR, "input[type='file']")
    attach.send_keys(midia)
    time.sleep(3)
    send = navegador.find_element(By.CSS_SELECTOR, "span[data-icon='send']")
    send.click()

def send_message(navegador, numero, mensagem):
    max_retries = 1  # Pode ser parametrizado via get_settings se necessário
    texto = urllib.parse.quote(mensagem)
    link = f"https://web.whatsapp.com/send?phone={numero}&text={texto}"

    if numero is None:
        return False, 'NONE_Number'
    try:
        for retry in range(1, max_retries + 1):
            try:
                navegador.get(link)
                WebDriverWait(navegador, timeout=50).until(EC.presence_of_element_located((By.ID, 'side')))
                time.sleep(10)
                break
            except TimeoutException:
                print(f"Timed out waiting for 'side'. Retry {retry} of {max_retries}...")
                if retry == max_retries:
                    return False, 'timeout_ERROR'
                time.sleep(10)
            except UnexpectedAlertPresentException:
                time.sleep(10)
                return False, 'Alert_text???'
        
        try:
            # Verifica se há botão indicando que o número não possui WhatsApp
            navegador.find_element(By.XPATH, '//*[@id="app"]/div/span[2]/div/span/div/div/div/div/div/div[2]/div/button').click()
            time.sleep(5)
            return False, 'NO_WHATSAPP_Number'
        except NoSuchElementException:
            pass

        try:
            navegador.find_element(By.XPATH, '//*[@id="main"]/footer/div[1]/div/span[2]/div/div[2]/div[2]').click()
            time.sleep(5)
            # Envia a primeira mídia
            enviar_midia(navegador, "images/2916315.png")
            time.sleep(3)
            # Envia a segunda mídia (para fins experimentais)
            enviar_midia(navegador, "images/2916315.png")
            time.sleep(7)
            return True, 'SUCCESS'
        except NoSuchElementException:
            time.sleep(5)
            return False, 'Element_ERROR'
        except InvalidArgumentException:
            time.sleep(5)
            return False, 'Image_ERROR'
    except Exception as e:
        time.sleep(10)
        return False, 'Ambiguious_general_ERROR'

def get_greeting_and_time(country_name):
    country_timezones = {
        'Afghanistan': 'Asia/Kabul',
        'Albania': 'Europe/Tirane',
        'Algeria': 'Africa/Algiers',
        'Andorra': 'Europe/Andorra',
        'Angola': 'Africa/Luanda',
        'Argentina': 'America/Argentina/Buenos_Aires',
        'Armenia': 'Asia/Yerevan',
        'Australia': 'Australia/Sydney',
        'Austria': 'Europe/Vienna',
        'Azerbaijan': 'Asia/Baku',
        'Bahamas': 'America/Nassau',
        'Bahrain': 'Asia/Bahrain',
        'Bangladesh': 'Asia/Dhaka',
        'Barbados': 'America/Barbados',
        'Belarus': 'Europe/Minsk',
        'Belgium': 'Europe/Brussels',
        'Belize': 'America/Belize',
        'Benin': 'Africa/Porto-Novo',
        'Bhutan': 'Asia/Thimphu',
        'Bolivia': 'America/La_Paz',
        'Bosnia and Herzegovina': 'Europe/Sarajevo',
        'Botswana': 'Africa/Gaborone',
        'Brazil': 'America/Sao_Paulo',
        'Brasil': 'America/Sao_Paulo',
        'Brunei': 'Asia/Brunei',
        'Bulgaria': 'Europe/Sofia',
        'Burkina Faso': 'Africa/Ouagadougou',
        'Burundi': 'Africa/Bujumbura',
        'Cambodia': 'Asia/Phnom_Penh',
        'Cameroon': 'Africa/Douala',
        'Canada': 'America/Toronto',
        'Cape Verde': 'Atlantic/Cape_Verde',
        'Central African Republic': 'Africa/Bangui',
        'Chad': 'Africa/Ndjamena',
        'Chile': 'America/Santiago',
        'China': 'Asia/Shanghai',
        'Colombia': 'America/Bogota',
        'Comoros': 'Indian/Comoro',
        'Congo (Brazzaville)': 'Africa/Brazzaville',
        'Congo (Kinshasa)': 'Africa/Kinshasa',
        'Costa Rica': 'America/Costa_Rica',
        'Croatia': 'Europe/Zagreb',
        'Cuba': 'America/Havana',
        'Cyprus': 'Asia/Nicosia',
        'Czech Republic': 'Europe/Prague',
        'Denmark': 'Europe/Copenhagen',
        'Djibouti': 'Africa/Djibouti',
        'Dominica': 'America/Dominica',
        'Dominican Republic': 'America/Santo_Domingo',
        'Ecuador': 'America/Guayaquil',
        'Egypt': 'Africa/Cairo',
        'El Salvador': 'America/El_Salvador',
        'Equatorial Guinea': 'Africa/Malabo',
        'Eritrea': 'Africa/Asmara',
        'Estonia': 'Europe/Tallinn',
        'Eswatini': 'Africa/Mbabane',
        'Ethiopia': 'Africa/Addis_Ababa',
        'Fiji': 'Pacific/Fiji',
        'Finland': 'Europe/Helsinki',
        'France': 'Europe/Paris',
        'Gabon': 'Africa/Libreville',
        'Gambia': 'Africa/Banjul',
        'Georgia': 'Asia/Tbilisi',
        'Germany': 'Europe/Berlin',
        'Ghana': 'Africa/Accra',
        'Greece': 'Europe/Athens',
        'Grenada': 'America/Grenada',
        'Guatemala': 'America/Guatemala',
        'Guinea': 'Africa/Conakry',
        'Guinea-Bissau': 'Africa/Bissau',
        'Guyana': 'America/Guyana',
        'Haiti': 'America/Port-au-Prince',
        'Honduras': 'America/Tegucigalpa',
        'Hungary': 'Europe/Budapest',
        'Iceland': 'Atlantic/Reykjavik',
        'India': 'Asia/Kolkata',
        'Indonesia': 'Asia/Jakarta',
        'Iran': 'Asia/Tehran',
        'Iraq': 'Asia/Baghdad',
        'Ireland': 'Europe/Dublin',
        'Israel': 'Asia/Jerusalem',
        'Italy': 'Europe/Rome',
        'Jamaica': 'America/Jamaica',
        'Japan': 'Asia/Tokyo',
        'Jordan': 'Asia/Amman',
        'Kazakhstan': 'Asia/Almaty',
        'Kenya': 'Africa/Nairobi',
        'Korea, North': 'Asia/Pyongyang',
        'Korea, South': 'Asia/Seoul',
        'Kuwait': 'Asia/Kuwait',
        'Kyrgyzstan': 'Asia/Bishkek',
        'Laos': 'Asia/Vientiane',
        'Latvia': 'Europe/Riga',
        'Lebanon': 'Asia/Beirut',
        'Lesotho': 'Africa/Maseru',
        'Liberia': 'Africa/Monrovia',
        'Libya': 'Africa/Tripoli',
        'Liechtenstein': 'Europe/Vaduz',
        'Lithuania': 'Europe/Vilnius',
        'Luxembourg': 'Europe/Luxembourg',
        'Madagascar': 'Indian/Antananarivo',
        'Malawi': 'Africa/Blantyre',
        'Malaysia': 'Asia/Kuala_Lumpur',
        'Maldives': 'Indian/Maldives',
        'Mali': 'Africa/Bamako',
        'Malta': 'Europe/Malta',
        'Marshall Islands': 'Pacific/Majuro',
        'Mauritania': 'Africa/Nouakchott',
        'Mauritius': 'Indian/Mauritius',
        'Mexico': 'America/Mexico_City',
        'Micronesia': 'Pacific/Pohnpei',
        'Moldova': 'Europe/Chisinau',
        'Monaco': 'Europe/Monaco',
        'Mongolia': 'Asia/Ulaanbaatar',
        'Montenegro': 'Europe/Podgorica',
        'Morocco': 'Africa/Casablanca',
        'Mozambique': 'Africa/Maputo',
        'Myanmar': 'Asia/Yangon',
        'Namibia': 'Africa/Windhoek',
        'Nauru': 'Pacific/Nauru',
        'Nepal': 'Asia/Kathmandu',
        'Netherlands': 'Europe/Amsterdam',
        'New Zealand': 'Pacific/Auckland',
        'Nicaragua': 'America/Managua',
        'Niger': 'Africa/Niamey',
        'Nigeria': 'Africa/Lagos',
        'North Macedonia': 'Europe/Skopje',
        'Norway': 'Europe/Oslo',
        'Oman': 'Asia/Muscat',
        'Pakistan': 'Asia/Karachi',
        'Palau': 'Pacific/Palau',
        'Panama': 'America/Panama',
        'Papua New Guinea': 'Pacific/Port_Moresby',
        'Paraguay': 'America/Asuncion',
        'Peru': 'America/Lima',
        'Philippines': 'Asia/Manila',
        'Poland': 'Europe/Warsaw',
        'Portugal': 'Europe/Lisbon',
        'Qatar': 'Asia/Qatar',
        'Romania': 'Europe/Bucharest',
        'Russia': 'Europe/Moscow',
        'Rwanda': 'Africa/Kigali',
        'Saint Kitts and Nevis': 'America/St_Kitts',
        'Saint Lucia': 'America/St_Lucia',
        'Saint Vincent and the Grenadines': 'America/St_Vincent',
        'Samoa': 'Pacific/Apia',
        'San Marino': 'Europe/San_Marino',
        'Sao Tome and Principe': 'Africa/Sao_Tome',
        'Saudi Arabia': 'Asia/Riyadh',
        'Senegal': 'Africa/Dakar',
        'Serbia': 'Europe/Belgrade',
        'Seychelles': 'Indian/Mahe',
        'Sierra Leone': 'Africa/Freetown',
        'Singapore': 'Asia/Singapore',
        'Slovakia': 'Europe/Bratislava',
        'Slovenia': 'Europe/Ljubljana',
        'Solomon Islands': 'Pacific/Guadalcanal',
        'Somalia': 'Africa/Mogadishu',
        'South Africa': 'Africa/Johannesburg',
        'South Sudan': 'Africa/Juba',
        'Spain': 'Europe/Madrid',
        'Sri Lanka': 'Asia/Colombo',
        'Sudan': 'Africa/Khartoum',
        'Suriname': 'America/Paramaribo',
        'Sweden': 'Europe/Stockholm',
        'Switzerland': 'Europe/Zurich',
        'Syria': 'Asia/Damascus',
        'Taiwan': 'Asia/Taipei',
        'Tajikistan': 'Asia/Dushanbe',
        'Tanzania': 'Africa/Dar_es_Salaam',
        'Thailand': 'Asia/Bangkok',
        'Togo': 'Africa/Lome',
        'Tonga': 'Pacific/Tongatapu',
        'Trinidad and Tobago': 'America/Port_of_Spain',
        'Tunisia': 'Africa/Tunis',
        'Turkey': 'Europe/Istanbul',
        'Turkmenistan': 'Asia/Ashgabat',
        'Tuvalu': 'Pacific/Funafuti',
        'Uganda': 'Africa/Kampala',
        'Ukraine': 'Europe/Kiev',
        'United Arab Emirates': 'Asia/Dubai',
        'United Kingdom': 'Europe/London',
        'United States': 'America/New_York',
        'Uruguay': 'America/Montevideo',
        'Uzbekistan': 'Asia/Tashkent',
        'Vanuatu': 'Pacific/Efate',
        'Vatican City': 'Europe/Vatican',
        'Venezuela': 'America/Caracas',
        'Vietnam': 'Asia/Ho_Chi_Minh',
        'Yemen': 'Asia/Aden',
        'Zambia': 'Africa/Lusaka',
        'Zimbabwe': 'Africa/Harare'
    }
    if country_name.casefold() in country_timezones:
        # Obtem o timezone correspondente e calcula a saudação com base na hora local
        timezone = pytz.timezone(country_timezones[country_name])
        current_time = datetime.datetime.now(timezone)
        hour = current_time.hour
        if 5 <= hour < 12:
            return 'Good morning!'
        elif 12 <= hour < 18:
            return 'Good afternoon!'
        else:
            return 'Good evening!'
    else:
        print(f"the country {country_name} is invalid, using defined avarage country time")
        return None

def get_time_local():
    # Retorna uma saudação em português com base na hora atual
    currentTime = int(time.strftime('%H'))
    if currentTime < 12:
        return 'Bom dia'
    if currentTime >= 12:
        return 'Boa tarde'
    if currentTime > 6:
        return 'Boa noite'

def extract_state_and_country(address):
    # Extrai o último item (país) a partir do endereço
    parts = address.split(',')
    return parts[-1].strip() if parts else ''

# ========= Funções principais =========

def main(excel_file, navegador):
    df = pd.read_excel(excel_file)
    total_rows = df.shape[0]
    errors = []
    succ_int = 0
    start_time = time.perf_counter()
    
    for index, row in df.iterrows():
        time_now = datetime.datetime.now().strftime("%H:%M")
        buss_name = row['name']
        buss_address = row['address']
        buss_number = row['phone_number']
        
        if MESSAGE_LANGUAGE == 'portugues':
            try:
                conditional_time = get_time_local()
            except:
                conditional_time = TIME_AVARAGE[0]
            mensagem = pt_txt(conditional_time, buss_name)
            buss_number = str('55' + str(buss_number))
        elif MESSAGE_LANGUAGE == 'ingles':
            country = extract_state_and_country(buss_address)
            if not country:
                country = CONTRY_AVARAGE
            conditional_time = get_greeting_and_time(country)
            if conditional_time is None:
                conditional_time = TIME_AVARAGE[1]
            mensagem = eng_txt(conditional_time, buss_name)
        
        send = send_message(navegador, buss_number, mensagem)
        print(f"""
Runned at {time_now}, Nº: {index}/{total_rows - 1}
Name: {buss_name}
LOG: {send}
-----------------------------------------------""")
        if not send[0]:
            errors.append({'Name': buss_name, 'Error': send[1]})
        else:
            succ_int += 1
    
    clear()
    finish_time = time.perf_counter()
    final_time = round(int(finish_time - start_time) / 60)
    print(f'''
Done Running! {time_now}, runned in {final_time} minutes
Successfull messages = {succ_int}/{total_rows - 1}
Errors = {len(errors)}/{total_rows - 1}''')
    
    details = "\n".join([f"{err['Name']}: {err['Error']}" for err in errors])
    if details:
        messagebox.showinfo("Erros", details)
    else:
        messagebox.showinfo("Status", "Todos os envios foram executados com sucesso.")

def test_message():
    number = simple_input("Digite um número para teste (com DDD, ex.: 11955555555):")
    if not number:
        return
    start_time = time.perf_counter()
    buss_name, buss_address = 'TEST BUSSINESS NAME', 'PLACEHOLDER'
    
    if MESSAGE_LANGUAGE == 'portugues':
        try:
            conditional_time = get_time_local()
        except:
            conditional_time = TIME_AVARAGE[0]
        mensagem = pt_txt(conditional_time, buss_name)
    elif MESSAGE_LANGUAGE == 'ingles':
        country = extract_state_and_country(buss_address)
        if not country:
            country = CONTRY_AVARAGE
        conditional_time = get_greeting_and_time(country)
        if conditional_time is None:
            conditional_time = TIME_AVARAGE[1]
        mensagem = eng_txt(conditional_time, buss_name)
    
    print("Abrindo navegador para teste...")
    navegador = get_navegador()
    send = send_message(navegador, number, mensagem)
    finish_time = time.perf_counter()
    final_time = round(int(finish_time - start_time) / 60)
    if not send[0]:
        messagebox.showinfo("Teste Mensagem", f"Falha! Motivo: {send[1]}\nTempo: {final_time} minutos")
    else:
        messagebox.showinfo("Teste Mensagem", f"Sucesso!\nTempo: {final_time} minutos")

def test_file():
    file_name = open_file_dialog()
    if not file_name:
        return
    df = pd.read_excel(file_name)
    total_rows = df.shape[0]
    details = ""
    for index, row in df.iterrows():
        current = datetime.datetime.now().strftime("%H:%M")
        buss_name = row.get('name', '')
        buss_address = row.get('address', '')
        buss_number = row.get('phone_number', '')
        buss_web_site = row.get('website', '')
        details += (f"\nRunned at {current}, Nº: {index}/{total_rows - 1}\n"
                    f"Name: {buss_name}\nAddress: {buss_address}\nNumber: {buss_number}\nWebsite: {buss_web_site}\n"
                    "------------------------------------------------\n")
    messagebox.showinfo("Teste de Arquivo", details)

def options():
    # Função experimental para configurar parâmetros – exemplo simples usando diálogo de input
    msg = (
        "Opções:\n"
        "1 - Alterar Language (ENG / PT-BR)\n"
        "2 - Visualizar/alterar mensagem atual\n"
        "3 - Visualizar/alterar arquivo de imagem (pode ser None ou múltiplos)\n"
        "4 - Configurar Default_Country_time\n\n"
        "Digite o número da opção desejada ou 'back' para voltar:"
    )
    op = simple_input(msg)
    if op is None or op.lower() == 'back':
        return
    # Aqui você pode expandir as opções de configuração conforme a necessidade
    messagebox.showinfo("Opções", f"Opção {op} selecionada. (Funcionalidade a ser implementada)")
    
def show_help():
    help_text = (
        "Project Info:\n"
        "  Ferramenta experimental de automação para consulta no Google Maps e envio automático via WhatsApp.\n\n"
        "Comandos disponíveis:\n"
        "  - Time Loop: Agenda o envio para um horário específico.\n"
        "  - Enviar Agora: Inicia o envio imediatamente.\n"
        "  - Consulta Google Maps: Executa consulta e salva resultados.\n"
        "  - Teste Mensagem: Testa o envio para um número.\n"
        "  - Teste Arquivo: Exibe conteúdo do arquivo selecionado.\n"
        "  - Opções: Configura parâmetros experimentais (idioma, mensagem, imagem, etc.).\n\n"
        "Acesse o repositório para mais informações."
    )
    messagebox.showinfo("Ajuda", help_text)

# Janela simples para input via Toplevel
def simple_input(prompt):
    top = Toplevel()
    top.title("Input")
    Label(top, text=prompt).pack(padx=10, pady=10)
    entry = Entry(top)
    entry.pack(padx=10, pady=10)
    entry.focus_set()
    result = {"value": None}

    def submit():
        result["value"] = entry.get()
        top.destroy()

    Button(top, text="OK", command=submit).pack(pady=10)
    top.wait_window()
    return result["value"]

# ========= Funções para a interface gráfica =========

def run_time_loop():
    file_path = open_file_dialog()
    if not file_path:
        return
    navegador = get_navegador()
    target_time = simple_input("Informe a hora para iniciar (formato HH:00):")
    if not target_time:
        return

    def wait_and_run():
        current = datetime.datetime.now().strftime("%H:%M")
        while current != target_time:
            print(f"Agora são {current}. Aguardando {target_time}...")
            time.sleep(60)
            gui.press('shift')
            current = datetime.datetime.now().strftime("%H:%M")
        messagebox.showinfo("Time Loop", f"Hora {target_time} alcançada! Iniciando envio.")
        main(file_path, navegador)
    threading.Thread(target=wait_and_run, daemon=True).start()

def run_start_now():
    file_path = open_file_dialog()
    if not file_path:
        return
    navegador = get_navegador()
    threading.Thread(target=lambda: main(file_path, navegador), daemon=True).start()

def run_google_query():
    search_term = simple_input("Informe o nome/termo para consulta:")
    location = simple_input("Informe a localização:")
    total = simple_input("Informe o número de resultados (número inteiro):")
    try:
        total = int(total)
    except:
        messagebox.showerror("Erro", "Valor inválido para número de resultados.")
        return
    search_for = f"{search_term} {location}"
    threading.Thread(target=lambda: main_query(search_for, total, location), daemon=True).start()

def run_test_message():
    threading.Thread(target=test_message, daemon=True).start()

def run_test_file():
    threading.Thread(target=test_file, daemon=True).start()

def run_options():
    threading.Thread(target=options, daemon=True).start()

# Cria a janela principal
def create_main_window():
    get_settings()  # Carrega as configurações experimentais
    root = Tk()
    root.title("Automação Experimental WhatsApp e Google Maps")
    root.geometry("400x500")
    
    Label(root, text="Selecione uma funcionalidade:", font=("Arial", 14)).pack(pady=10)
    Button(root, text="Time Loop", width=20, command=run_time_loop).pack(pady=5)
    Button(root, text="Enviar Agora", width=20, command=run_start_now).pack(pady=5)
    Button(root, text="Consulta Google Maps", width=20, command=run_google_query).pack(pady=5)
    Button(root, text="Teste Mensagem", width=20, command=run_test_message).pack(pady=5)
    Button(root, text="Teste Arquivo", width=20, command=run_test_file).pack(pady=5)
    Button(root, text="Opções", width=20, command=run_options).pack(pady=5)
    Button(root, text="Ajuda", width=20, command=show_help).pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    create_main_window()
