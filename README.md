<div align="center">
  <img src="assets/img/logo.png" alt="PROSPECTO" width="220"/>
  
# PROSPECTO
</div>

## Sobre o projeto

**PROSPECTO** é uma aplicação desktop com interface gráfica desenvolvida em Python e PyQt6 para envio automatizado de mensagens via WhatsApp Web. Ideal para campanhas, notificações ou comunicação em massa, permite enviar mensagens personalizadas para uma lista de contatos a partir de uma planilha Excel, de forma simples, rápida e segura.

---

## Funcionalidades

- Interface amigável e intuitiva
- Geração e download de template Excel para contatos
- Carregamento de planilhas Excel com contatos e mensagens personalizadas
- Visualização prévia dos contatos e mensagens antes do envio
- Configuração do intervalo entre envios (10 a 120 segundos)
- Envio automatizado via WhatsApp Web utilizando Selenium
- Barra de progresso e status em tempo real
- Log das mensagens enviadas
- Salvar e carregar configurações personalizadas
- Suporte para envio de arquivos de mídia (via código)
- Agendamento de envios (via código)

---

## Tecnologias utilizadas

- **Python 3.8+**
- **PyQt6** — Interface gráfica
- **Selenium** — Automação do navegador
- **webdriver-manager** — Gerenciamento do driver do Chrome
- **pandas, openpyxl** — Manipulação de arquivos Excel
- **schedule** — Agendamento de tarefas
- **python-dotenv** — Variáveis de ambiente
- **requests, pillow** — Suporte adicional

---

## Pré-requisitos

- **Google Chrome** instalado
- **Python 3.8 ou superior** instalado
- Variáveis de ambiente configuradas em um arquivo `.env` (opcional, para configurações avançadas)

---

## Instalação

1. Clone este repositório:

```bash
git clone https://github.com/seu-usuario/PROSPECTO.git
cd PROSPECTO
```

2. Crie um ambiente virtual (opcional, mas recomendado):

```bash
python -m venv venv
venv\Scripts\activate  # Windows
# ou
source venv/bin/activate  # Linux/macOS
```

3. Instale as dependências:

```bash
pip install -r requirements.txt
```

---

## Como usar

1. Execute o aplicativo:

```bash
python main.py
```

2. Na interface:
   - Clique em **📥 Download Template** para baixar um modelo de planilha.
   - Preencha a planilha com os números de telefone e mensagens personalizadas.
   - Clique em **📂 Carregar Arquivo** para importar sua planilha.
   - Ajuste o intervalo entre mensagens conforme desejado.
   - Clique em **📤 Enviar Mensagens** para iniciar o envio automático.

3. Acompanhe o progresso e o status na interface.

---

## Estrutura da planilha Excel

A planilha deve conter obrigatoriamente as seguintes colunas:

| telefone         | mensagem                 |
|------------------|--------------------------|
| +5511999999999   | Sua mensagem personalizada |
| 11999999999      | Outra mensagem            |

- O número pode estar com ou sem o código do país.
- A mensagem pode ser personalizada para cada contato.

---

## Configurações adicionais

- **Intervalo entre mensagens:** ajustável na interface (10 a 120 segundos).
- **Preview:** opção para visualizar as mensagens antes do envio.
- **Salvar/Carregar configurações:** menu para exportar ou importar suas preferências.
- **Logs:** as mensagens enviadas são registradas no arquivo `log.txt`.
- **Envio de mídia:** disponível via código (função `send_whatsapp_media`).
- **Agendamento:** disponível via código (função `schedule_message`).

---

## Créditos

Desenvolvido por [Claudio Henrique]

💜 PIX para doações: **<EMAIL>**

---

## Licença

Este projeto está licenciado sob a [sua licença aqui].

---

## Suporte

Para dúvidas ou suporte, entre em contato pelo e-mail: **<EMAIL>**

- nao pegar leads repetidos
- se movimentar