import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suprime a maioria das mensagens do TensorFlow

from dataclasses import dataclass, asdict, field
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
import time

@dataclass
class Business:
    """Armazena os dados de um negócio."""
    name: str = None
    address: str = None
    website: str = None
    phone_number: str = None

@dataclass
class BusinessList:
    """Armazena uma lista de objetos Business e permite salvar os resultados em Excel ou CSV."""
    business_list: list[Business] = field(default_factory=list)
    
    def dataframe(self):
        """Transforma a lista de negócios em um DataFrame do pandas."""
        return pd.json_normalize((asdict(business) for business in self.business_list), sep="_")
    
    def save_to_excel(self, filename, save_dir):
        """Salva o DataFrame em um arquivo Excel."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.xlsx'
            self.dataframe().to_excel(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"
    
    def save_to_csv(self, filename, save_dir):
        """Salva o DataFrame em um arquivo CSV."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.csv'
            self.dataframe().to_csv(full_path, index=False, sep=';')
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

def move_map(navegador, direction='right'):
    """
    Move o mapa na direção especificada.
    direction: 'right', 'left', 'up', 'down'
    """
    # Primeiro clica no mapa para garantir que está focado
    map_element = navegador.find_element(By.CLASS_NAME, 'widget-scene')
    action = ActionChains(navegador)
    action.move_to_element(map_element).click().perform()
    time.sleep(1)
    
    # Define quantas vezes pressionar a tecla de seta
    presses = 10
    
    # Mapeia a direção para a tecla correspondente
    key_map = {
        'right': Keys.ARROW_RIGHT,
        'left': Keys.ARROW_LEFT,
        'up': Keys.ARROW_UP,
        'down': Keys.ARROW_DOWN
    }
    
    # Pressiona a tecla várias vezes para mover o mapa
    key = key_map.get(direction, Keys.ARROW_RIGHT)
    for _ in range(presses):
        action.send_keys(key).perform()
        time.sleep(0.1)
    
    # Aguarda um pouco para o mapa carregar
    time.sleep(3)

def main_query(search_for, total, location, save_dir, file_format, callback=None):
    """
    Executa a consulta no Google Maps e extrai os dados dos estabelecimentos.
    
    Parâmetros:
      search_for: termo de busca (ex.: "restaurante Rio de Janeiro")
      total: número de resultados desejados
      location: localização para refinar a consulta
      save_dir: diretório onde os resultados serão salvos
      file_format: "excel" ou "csv"
      callback: função opcional para receber atualizações de status
    """
    # Configura o Chrome para iniciar maximizado
    from selenium.webdriver.chrome.options import Options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-gpu")  # Desativa aceleração por hardware
    chrome_options.add_argument("--enable-unsafe-swiftshader")  # Permite SwiftShader
    chrome_options.add_argument("--disable-software-rasterizer")  # Evita problemas com renderização
    chrome_options.add_argument("--disable-dev-shm-usage")  # Evita problemas de memória compartilhada
    chrome_options.add_argument("--no-sandbox")  # Aumenta compatibilidade
    chrome_options.add_experimental_option("detach", True)  # Mantém o navegador aberto
    chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])  # Remove barra de automação
    chrome_options.add_experimental_option("excludeSwitches", ['enable-logging'])  # Remove logs do DevTools
    
    if callback:
        callback("[INFO] Iniciando o navegador Chrome em modo headless...")
    
    navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    
    if callback:
        callback("[INFO] Abrindo Google Maps...")
    navegador.get("https://www.google.com.br/maps")
    time.sleep(3)
    
    # Insere a localização e executa a busca inicial
    if callback:
        callback(f"[INFO] Buscando localização: {location}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(location)
    time.sleep(2)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(15)
    navegador.find_element(By.XPATH, '//*[@id="searchbox"]/div[2]/button').click()
    time.sleep(5)
    
    # Realiza a busca do termo combinado (negócio + localização)
    if callback:
        callback(f"[INFO] Buscando termo: {search_for}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(search_for)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(10)
    
    business_list = BusinessList()
    i = 0
    move_count = 0
    max_moves = 4  # Número máximo de movimentos do mapa
    
    while i < total and move_count < max_moves:
        previously_counted = 0
        stuck_count = 0
        
        while True:
            list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            if not list_elem:
                if callback:
                    callback("[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região.")
                return "[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região."

            action = ActionChains(navegador)
            try:
                action.move_to_element(list_elem[-1]).perform()
            except Exception:
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                action.move_to_element(list_elem[-1]).perform()
            time.sleep(5)
            
            scroll_origin = ScrollOrigin.from_element(list_elem[-1])
            action.scroll_from_origin(scroll_origin, 0, 1200).perform()
            time.sleep(20)
            action.scroll_from_origin(scroll_origin, 0, 250).perform()
            
            current_count = len(list_elem)
            if current_count == previously_counted:
                stuck_count += 1
                if stuck_count >= 3:  # Se ficar preso 3 vezes, consideramos que chegamos ao limite
                    break
            else:
                stuck_count = 0
                previously_counted = current_count

        # Processa os elementos encontrados
        list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
        for element in list_elem[i:]:
            if i >= total:
                break
                
            try:
                time.sleep(2)
                navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(2)
                try:
                    element.click()
                except Exception as click_err:
                    error_msg = f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}"
                    if callback:
                        callback(error_msg)
                    print(error_msg)
                    i += 1
                    continue
                time.sleep(6)

                # XPaths para extração dos dados
                name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
                address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
                website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
                phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'
        
                business = Business()
                try:
                    business.name = navegador.find_element(By.XPATH, name_xpath).text or ''
                    if not business.name:
                        business.name = navegador.find_element(By.XPATH, "//h1[contains(@class, 'fontHeadlineLarge')]").text or ''
                except NoSuchElementException:
                    business.name = ''
                try:
                    business.address = navegador.find_element(By.XPATH, address_xpath).text or ''
                except NoSuchElementException:
                    business.address = ''
                try:
                    business.website = navegador.find_element(By.XPATH, website_xpath).text or ''
                except NoSuchElementException:
                    business.website = ''
                try:
                    business.phone_number = navegador.find_element(By.XPATH, phone_number_xpath).text or ''
                except NoSuchElementException:
                    business.phone_number = ''
        
                business_list.business_list.append(business)
                time.sleep(3)
                i += 1
                
                if callback:
                    porcentagem = (i / total) * 100
                    callback(f"[PROGRESSO] Processando registro {i} de {total} ({porcentagem:.1f}%)")
                    
            except StaleElementReferenceException:
                error_msg = f"[AVISO] Elemento {i} está desatualizado, tentando próximo registro..."
                if callback:
                    callback(error_msg)
                print(error_msg)
                i += 1
                continue
        
        # Se ainda não atingimos o total desejado, pergunta se quer continuar
        if i < total:
            if callback:
                msg = f"[INFO] Encontrados {i} de {total} registros desejados.\n[AÇÃO] Deseja continuar a busca movendo o mapa para encontrar mais resultados? (S/N)"
                response = callback(msg, require_response=True)
                if response and response.upper() == 'S':
                    # Move o mapa em diferentes direções a cada iteração
                    directions = ['right', 'down', 'left', 'up']
                    move_map(navegador, directions[move_count % len(directions)])
                    move_count += 1
                    time.sleep(5)  # Aguarda o mapa carregar
                else:
                    break
            else:
                break

    # Finaliza a extração e salva automaticamente os dados
    updated_string = search_for.replace(" ", "_")
    result = None
    if file_format.lower() == "excel":
        result = business_list.save_to_excel(f'maps_data_{updated_string}', save_dir)
    elif file_format.lower() == "csv":
        result = business_list.save_to_csv(f'maps_data_{updated_string}', save_dir)
    else:
        result = "Formato de arquivo inválido."
    
    if callback:
        callback(result)
    return result

if __name__ == "__main__":
    search_term = input("Nome para consulta:")
    location = input("Localização para consulta:")
    search_for = f"{search_term} {location}"
    total_str = input("Número de locais para consulta (inteiro):")
    try:
        total = int(total_str)
    except:
        print("Valor inválido para número de locais.")
        exit(1)
    
    def console_callback(msg, require_response=False):
        print(msg)
        if require_response:
            return input("Sua resposta: ")
    
    main_query(search_for, total, location, ".", "excel", callback=console_callback)
