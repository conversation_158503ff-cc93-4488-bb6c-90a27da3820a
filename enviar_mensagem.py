"""
Message selection and usage tracking for sending messages.
"""
import argparse
import json
from datetime import datetime

from utils.db_operations import get_unused_messages, mark_message_as_used, export_used_messages_to_csv
from utils.filtro_similaridade import pode_enviar

def get_next_message():
    """
    Get the next unused message from the database.
    
    Returns:
        dict: Message dictionary or None if no unused messages are available
    """
    messages = get_unused_messages(limit=1)
    return messages[0] if messages else None

def mark_as_sent(message_id, conversion=False):
    """
    Mark a message as sent.
    
    Args:
        message_id (int): The message ID
        conversion (bool, optional): Whether the message resulted in a conversion. Defaults to False.
        
    Returns:
        bool: True if marked successfully, False otherwise
    """
    return mark_message_as_used(message_id, conversion)

def export_sent_messages():
    """
    Export sent messages to a CSV file.
    
    Returns:
        str: Path to the exported CSV file
    """
    return export_used_messages_to_csv()

def main():
    """Main function to get and mark messages as sent."""
    parser = argparse.ArgumentParser(description="Get and mark messages as sent")
    parser.add_argument("--action", "-a", choices=["get", "mark", "export"], required=True, 
                        help="Action to perform: get a message, mark as sent, or export sent messages")
    parser.add_argument("--id", "-i", type=int, help="Message ID (required for 'mark' action)")
    parser.add_argument("--conversion", "-c", action="store_true", help="Mark message as resulting in conversion")
    
    args = parser.parse_args()
    
    try:
        if args.action == "get":
            message = get_next_message()
            if message:
                print(json.dumps(message, indent=2))
            else:
                print("No unused messages available.")
        
        elif args.action == "mark":
            if not args.id:
                raise ValueError("Message ID is required for 'mark' action")
            
            success = mark_as_sent(args.id, args.conversion)
            if success:
                print(f"Message {args.id} marked as sent successfully.")
            else:
                print(f"Failed to mark message {args.id} as sent.")
        
        elif args.action == "export":
            file_path = export_sent_messages()
            print(f"Sent messages exported to {file_path}")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
